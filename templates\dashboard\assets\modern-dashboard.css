/**
 * 🎨 BiNDup Modern Dashboard - Advanced CSS
 * Ultra-modern design with glassmorphism, neumorphism, and micro-interactions
 */

/* ===== MODERN RESET & BASE ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

:root {
    --primary-50: #eff6ff;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-900: #1e3a8a;
    
    --glass-light: rgba(255, 255, 255, 0.1);
    --glass-dark: rgba(0, 0, 0, 0.1);
    
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --shadow-neumorphism-light: 20px 20px 60px #bebebe, -20px -20px 60px #ffffff;
    --shadow-neumorphism-dark: 20px 20px 60px #1a202c, -20px -20px 60px #4a5568;
    
    --border-radius-xl: 1.5rem;
    --border-radius-2xl: 2rem;
    --border-radius-3xl: 3rem;
    
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #1f2937;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.dark body {
    color: #f9fafb;
    background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 100%);
}

/* ===== MODERN GLASSMORPHISM ===== */
.glass-morphism {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: var(--shadow-glass);
    position: relative;
    overflow: hidden;
}

.dark .glass-morphism {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.glass-morphism::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.glass-morphism:hover::before {
    left: 100%;
}

/* ===== ADVANCED NEUMORPHISM ===== */
.neumorphism {
    background: linear-gradient(145deg, #f0f0f0, #cacaca);
    box-shadow: var(--shadow-neumorphism-light);
    border: none;
    transition: var(--transition-smooth);
}

.dark .neumorphism {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    box-shadow: var(--shadow-neumorphism-dark);
}

.neumorphism:hover {
    box-shadow: inset 5px 5px 10px #bebebe, inset -5px -5px 10px #ffffff;
}

.dark .neumorphism:hover {
    box-shadow: inset 5px 5px 10px #1a202c, inset -5px -5px 10px #4a5568;
}

/* ===== MODERN ANIMATIONS ===== */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(-5px) rotate(-1deg); }
}

@keyframes pulse-glow {
    0%, 100% { 
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
        transform: scale(1);
    }
    50% { 
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
        transform: scale(1.05);
    }
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes morph-blob {
    0%, 100% { 
        border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
        transform: rotate(0deg) scale(1);
    }
    25% { 
        border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
        transform: rotate(90deg) scale(1.1);
    }
    50% { 
        border-radius: 50% 50% 50% 50% / 50% 50% 50% 50%;
        transform: rotate(180deg) scale(0.9);
    }
    75% { 
        border-radius: 70% 30% 40% 60% / 40% 70% 60% 30%;
        transform: rotate(270deg) scale(1.05);
    }
}

@keyframes particle-float {
    0%, 100% { 
        transform: translateY(0px) translateX(0px) rotate(0deg);
        opacity: 0.7;
    }
    25% { 
        transform: translateY(-20px) translateX(10px) rotate(90deg);
        opacity: 1;
    }
    50% { 
        transform: translateY(-10px) translateX(-10px) rotate(180deg);
        opacity: 0.5;
    }
    75% { 
        transform: translateY(-30px) translateX(5px) rotate(270deg);
        opacity: 0.8;
    }
}

@keyframes slide-in-bounce {
    0% {
        transform: translateX(-100%) scale(0.8);
        opacity: 0;
    }
    60% {
        transform: translateX(10%) scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: translateX(0%) scale(1);
        opacity: 1;
    }
}

@keyframes scale-bounce {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes text-glow {
    0%, 100% {
        text-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    50% {
        text-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
    }
}

/* ===== MODERN UTILITY CLASSES ===== */
.floating-element {
    animation: float 6s ease-in-out infinite;
}

.morphing-blob {
    animation: morph-blob 8s ease-in-out infinite;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
}

.gradient-bg {
    background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    animation: gradient-shift 15s ease infinite;
}

.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: text-glow 3s ease-in-out infinite;
}

.hover-lift {
    transition: var(--transition-smooth);
    cursor: pointer;
}

.hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.hover-glow:hover {
    animation: pulse-glow 2s ease-in-out infinite;
}

/* ===== MODERN RIPPLE EFFECT ===== */
.ripple {
    position: relative;
    overflow: hidden;
    transform: translate3d(0, 0, 0);
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
    pointer-events: none;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* ===== MODERN CARDS ===== */
.modern-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-2xl);
    padding: 2rem;
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.modern-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
}

.modern-card:hover::after {
    opacity: 1;
    animation: particle-float 3s ease-in-out infinite;
}

.modern-card:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: 0 30px 60px -12px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

/* ===== MODERN BUTTONS ===== */
.modern-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: var(--border-radius-xl);
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modern-btn:hover::before {
    left: 100%;
}

.modern-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 15px 30px -5px rgba(102, 126, 234, 0.4);
}

.modern-btn:active {
    transform: translateY(0) scale(0.98);
}

/* ===== MODERN PROGRESS INDICATORS ===== */
.modern-progress {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 9999px;
    overflow: hidden;
    position: relative;
}

.modern-progress-fill {
    height: 100%;
    border-radius: 9999px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.modern-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 50px 50px;
    animation: progress-stripes 2s linear infinite;
}

@keyframes progress-stripes {
    0% { background-position: 0 0; }
    100% { background-position: 50px 50px; }
}

/* ===== MODERN TOOLTIPS ===== */
.modern-tooltip {
    position: relative;
    display: inline-block;
}

.modern-tooltip .tooltip-content {
    visibility: hidden;
    width: 200px;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    color: #fff;
    text-align: center;
    border-radius: var(--border-radius-xl);
    padding: 12px 16px;
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: var(--transition-smooth);
    font-size: 14px;
    line-height: 1.4;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-tooltip .tooltip-content::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
}

.modern-tooltip:hover .tooltip-content {
    visibility: visible;
    opacity: 1;
    transform: translateY(-5px);
}

/* ===== MODERN SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .modern-card {
        padding: 1.5rem;
    }
    
    .hover-lift:hover {
        transform: none;
    }
    
    .modern-btn:hover {
        transform: none;
    }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (prefers-contrast: high) {
    .glass-morphism {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid #000;
    }
    
    .dark .glass-morphism {
        background: rgba(0, 0, 0, 0.9);
        border: 2px solid #fff;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    body {
        background: white !important;
        color: black !important;
    }
    
    .glass-morphism {
        background: white !important;
        backdrop-filter: none !important;
        border: 1px solid #ccc !important;
        box-shadow: none !important;
    }
    
    .gradient-bg {
        background: #f8fafc !important;
    }
    
    .floating-element,
    .morphing-blob {
        animation: none !important;
    }
}

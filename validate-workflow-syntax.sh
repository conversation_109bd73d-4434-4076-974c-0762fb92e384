#!/bin/bash

echo "🔍 Validating BiNDup Automation GitHub Actions Workflow Syntax..."

WORKFLOW_FILE=".github/workflows/bindup-automation-tests.yml"

# Check if workflow file exists
if [ ! -f "$WORKFLOW_FILE" ]; then
    echo "❌ Workflow file not found: $WORKFLOW_FILE"
    exit 1
fi

echo "✅ Workflow file found: $WORKFLOW_FILE"

# Test 1: Basic YAML syntax validation using Python
echo ""
echo "🔍 Test 1: YAML Syntax Validation..."
if command -v python3 &> /dev/null; then
    if python3 -c "
import yaml
import sys
try:
    with open('$WORKFLOW_FILE', 'r') as f:
        yaml.safe_load(f)
    print('✅ YAML syntax is valid')
except yaml.YAMLError as e:
    print(f'❌ YAML syntax error: {e}')
    sys.exit(1)
except Exception as e:
    print(f'❌ Error reading file: {e}')
    sys.exit(1)
"; then
        echo "✅ YAML validation passed"
    else
        echo "❌ YAML validation failed"
        exit 1
    fi
else
    echo "⚠️ Python3 not available, skipping YAML syntax check"
fi

# Test 2: Check for required files
echo ""
echo "🔍 Test 2: Project Structure Check..."

if [ -f "package.json" ]; then
    echo "✅ package.json found"
else
    echo "❌ package.json not found"
    exit 1
fi

if [ -f "playwright.config.ts" ]; then
    echo "✅ playwright.config.ts found"
else
    echo "❌ playwright.config.ts not found"
    exit 1
fi

echo ""
echo "🎉 Workflow validation completed successfully!"
echo "🚀 The workflow should work correctly on GitHub Actions!"

const { Page, FrameLocator } = require('@playwright/test');
// Use console.log for now since TestLogger import path varies
// const { TestLogger } = require('./test-logger');
const { AdvancedPageStateManager } = require('./advanced-page-state-manager');

/**
 * Iframe Stability Framework
 * Provides robust iframe context management with automatic reconnection
 * and state preservation during BiNDup operations
 */
export class IframeStabilityFramework {
  private page: Page;
  private pageStateManager: AdvancedPageStateManager;
  private iframeName: string;
  private currentFrame: FrameLocator | null = null;
  private lastKnownState: any = {};
  private reconnectionAttempts: number = 0;
  private maxReconnectionAttempts: number = 3;
  private isMonitoring: boolean = false;

  constructor(page: Page, pageStateManager: AdvancedPageStateManager, iframeName: string = 'preview') {
    this.page = page;
    this.pageStateManager = pageStateManager;
    this.iframeName = iframeName;
    
    console.log(`🖼️ Iframe Stability Framework initialized for: ${iframeName}`);
  }

  /**
   * Get stable iframe context with automatic reconnection
   */
  async getStableIframe(): Promise<FrameLocator | null> {
    console.log('🔍 Getting stable iframe context');

    try {
      // Check if page is stable first
      const isPageStable = await this.pageStateManager.isPageStable();
      if (!isPageStable) {
        TestLogger.logStep('⚠️ Page is not stable, waiting for stability', 'warning');
        await this.pageStateManager.waitForStableState(15000);
      }

      // Try to get iframe context
      const iframe = await this.acquireIframeContext();
      
      if (iframe) {
        this.currentFrame = iframe;
        this.reconnectionAttempts = 0;
        TestLogger.logStep('✅ Stable iframe context acquired', 'success');
        return iframe;
      }

      // If failed, attempt reconnection
      return await this.attemptIframeReconnection();

    } catch (error) {
      TestLogger.logStep(`❌ Failed to get stable iframe: ${error}`, 'error');
      return await this.attemptIframeReconnection();
    }
  }

  /**
   * Execute operation with iframe stability protection
   */
  async executeWithStability<T>(
    operation: (iframe: FrameLocator) => Promise<T>,
    operationName: string,
    maxRetries: number = 3
  ): Promise<T> {
    TestLogger.logStep(`🛡️ Executing ${operationName} with iframe stability protection`, 'start');

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Get stable iframe
        const iframe = await this.getStableIframe();
        
        if (!iframe) {
          throw new Error('Could not acquire stable iframe context');
        }

        // Execute operation
        TestLogger.logStep(`🔄 Executing ${operationName} (attempt ${attempt})`, 'start');
        const result = await operation(iframe);
        
        TestLogger.logStep(`✅ ${operationName} completed successfully`, 'success');
        return result;

      } catch (error) {
        TestLogger.logStep(`⚠️ ${operationName} attempt ${attempt} failed: ${error}`, 'warning');
        
        if (attempt === maxRetries) {
          TestLogger.logStep(`❌ ${operationName} failed after ${maxRetries} attempts`, 'error');
          throw error;
        }

        // Wait before retry and attempt iframe recovery
        await this.page.waitForTimeout(2000 * attempt);
        await this.attemptIframeReconnection();
      }
    }

    throw new Error(`Operation ${operationName} failed after all retries`);
  }

  /**
   * Check if iframe is accessible and responsive
   */
  async isIframeAccessible(iframe: FrameLocator): Promise<boolean> {
    try {
      // Try to access iframe content
      const frame = iframe.contentFrame();
      if (!frame) {
        return false;
      }

      // Check if iframe document is ready
      const isReady = await frame.evaluate(() => {
        return document.readyState === 'complete';
      });

      return isReady;

    } catch (error) {
      TestLogger.logStep(`⚠️ Iframe accessibility check failed: ${error}`, 'warning');
      return false;
    }
  }

  /**
   * Wait for iframe to become stable and ready
   */
  async waitForIframeStability(timeoutMs: number = 30000): Promise<boolean> {
    TestLogger.logStep('⏳ Waiting for iframe stability', 'start');
    
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeoutMs) {
      try {
        const iframe = this.page.frameLocator(`iframe[name="${this.iframeName}"]`);
        
        if (await this.isIframeAccessible(iframe)) {
          TestLogger.logStep('✅ Iframe is now stable', 'success');
          return true;
        }
        
        await this.page.waitForTimeout(1000);
        
      } catch (error) {
        // Continue waiting
      }
    }

    TestLogger.logStep('⚠️ Iframe stability timeout reached', 'warning');
    return false;
  }

  /**
   * Save current iframe state for recovery
   */
  async saveIframeState(iframe: FrameLocator): Promise<void> {
    try {
      const frame = iframe.contentFrame();
      if (!frame) return;

      // Save basic state information
      this.lastKnownState = {
        url: await frame.url(),
        title: await frame.title(),
        timestamp: Date.now()
      };

      TestLogger.logStep('💾 Iframe state saved for recovery', 'success');

    } catch (error) {
      TestLogger.logStep(`⚠️ Failed to save iframe state: ${error}`, 'warning');
    }
  }

  /**
   * Restore iframe state after reconnection
   */
  async restoreIframeState(iframe: FrameLocator): Promise<void> {
    try {
      if (!this.lastKnownState.url) return;

      const frame = iframe.contentFrame();
      if (!frame) return;

      // Check if we need to navigate to restore state
      const currentUrl = await frame.url();
      
      if (currentUrl !== this.lastKnownState.url) {
        TestLogger.logStep('🔄 Restoring iframe state', 'start');
        // Note: In BiNDup, iframe navigation is typically handled by the parent page
        // So we just log the state difference
        TestLogger.logStep(`📍 State difference detected: ${currentUrl} vs ${this.lastKnownState.url}`, 'warning');
      }

      TestLogger.logStep('✅ Iframe state restoration completed', 'success');

    } catch (error) {
      TestLogger.logStep(`⚠️ Failed to restore iframe state: ${error}`, 'warning');
    }
  }

  /**
   * Acquire iframe context with multiple strategies
   */
  private async acquireIframeContext(): Promise<FrameLocator | null> {
    const strategies = [
      // Strategy 1: Direct iframe access
      () => this.page.frameLocator(`iframe[name="${this.iframeName}"]`),
      
      // Strategy 2: Wait for iframe to load first
      async () => {
        await this.page.locator(`iframe[name="${this.iframeName}"]`).waitFor({ 
          state: 'attached', 
          timeout: 10000 
        });
        return this.page.frameLocator(`iframe[name="${this.iframeName}"]`);
      },
      
      // Strategy 3: Wait for iframe content to be ready
      async () => {
        const iframe = this.page.frameLocator(`iframe[name="${this.iframeName}"]`);
        await iframe.locator('body').waitFor({ timeout: 10000 });
        return iframe;
      }
    ];

    for (let i = 0; i < strategies.length; i++) {
      try {
        TestLogger.logStep(`🔄 Trying iframe acquisition strategy ${i + 1}`, 'start');
        
        const iframe = await strategies[i]();
        
        if (await this.isIframeAccessible(iframe)) {
          TestLogger.logStep(`✅ Iframe acquired with strategy ${i + 1}`, 'success');
          return iframe;
        }
        
      } catch (error) {
        TestLogger.logStep(`⚠️ Strategy ${i + 1} failed: ${error}`, 'warning');
      }
    }

    return null;
  }

  /**
   * Attempt iframe reconnection with recovery
   */
  private async attemptIframeReconnection(): Promise<FrameLocator | null> {
    if (this.reconnectionAttempts >= this.maxReconnectionAttempts) {
      TestLogger.logStep('❌ Maximum iframe reconnection attempts reached', 'error');
      return null;
    }

    this.reconnectionAttempts++;
    TestLogger.logStep(`🔄 Attempting iframe reconnection (${this.reconnectionAttempts}/${this.maxReconnectionAttempts})`, 'start');

    try {
      // First, ensure page is stable
      await this.pageStateManager.waitForStableState(10000);

      // Wait for iframe to stabilize
      const isStable = await this.waitForIframeStability(15000);
      
      if (!isStable) {
        TestLogger.logStep('⚠️ Iframe did not stabilize, but continuing', 'warning');
      }

      // Try to acquire iframe context again
      const iframe = await this.acquireIframeContext();
      
      if (iframe) {
        // Restore state if possible
        await this.restoreIframeState(iframe);
        
        TestLogger.logStep('✅ Iframe reconnection successful', 'success');
        return iframe;
      }

      TestLogger.logStep('⚠️ Iframe reconnection failed', 'warning');
      return null;

    } catch (error) {
      TestLogger.logStep(`❌ Iframe reconnection error: ${error}`, 'error');
      return null;
    }
  }

  /**
   * Create iframe operation with built-in stability
   */
  createStableOperation<T>(
    operation: (iframe: FrameLocator) => Promise<T>,
    operationName: string
  ): () => Promise<T> {
    return async () => {
      return await this.executeWithStability(operation, operationName);
    };
  }

  /**
   * Get iframe with fallback strategies
   */
  async getIframeWithFallback(): Promise<FrameLocator | null> {
    // Try primary method
    let iframe = await this.getStableIframe();
    
    if (iframe) {
      return iframe;
    }

    // Fallback: Force page refresh and retry
    TestLogger.logStep('🔄 Using fallback: page refresh and iframe retry', 'warning');
    
    try {
      await this.page.reload({ waitUntil: 'domcontentloaded', timeout: 30000 });
      await this.page.waitForTimeout(3000);
      
      iframe = await this.getStableIframe();
      
      if (iframe) {
        TestLogger.logStep('✅ Iframe acquired after fallback refresh', 'success');
        return iframe;
      }
      
    } catch (error) {
      TestLogger.logStep(`⚠️ Fallback refresh failed: ${error}`, 'warning');
    }

    TestLogger.logStep('❌ All iframe acquisition methods failed', 'error');
    return null;
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.isMonitoring = false;
    this.currentFrame = null;
    this.lastKnownState = {};
    this.reconnectionAttempts = 0;
    
    TestLogger.logStep('🧹 Iframe Stability Framework cleaned up', 'success');
  }
}

// Export for CommonJS
module.exports = { IframeStabilityFramework };

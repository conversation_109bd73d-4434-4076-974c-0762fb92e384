{"compilerOptions": {"target": "ES2022", "lib": ["ES2022", "DOM"], "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": false, "outDir": "./dist", "rootDir": "./", "baseUrl": "./", "paths": {"@pages/*": ["pages/*"], "@fixtures/*": ["fixtures/*"], "@utils/*": ["utils/*"], "@config/*": ["config/*"], "@data/*": ["data/*"], "@types/*": ["types/*"]}, "types": ["node", "@playwright/test"]}, "include": ["pages/**/*", "fixtures/**/*", "utils/**/*", "config/**/*", "data/**/*", "types/**/*", "tests/**/*", "playwright.config.ts"], "exclude": ["node_modules", "dist", "test-results", "playwright-report"]}
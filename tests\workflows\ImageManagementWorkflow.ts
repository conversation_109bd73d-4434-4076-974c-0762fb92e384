import { Page } from '@playwright/test';
import { TestLogger } from '../../utils/test-metrics';
import { executeWithRetry } from '../../utils/performance-utils';
import { BiNDupImageManagementPage } from '../pages/BiNDupImageManagementPage';
import { BiNDupAuthPage } from '../pages/BiNDupAuthPage';
import { TestUsers, UserCredentials } from '../../data/test-data';

export interface ImageOperationResult {
  success: boolean;
  imageId?: string;
  error?: string;
  duration?: number;
}

export interface ImageManagementWorkflowConfig {
  retryAttempts: number;
  timeouts: {
    navigation: number;
    operation: number;
    upload: number;
  };
  performanceMode: boolean;
}

export class ImageManagementWorkflow {
  private authPage: BiNDupAuthPage;
  private imageManagementPage: BiNDupImageManagementPage;
  private editorPageHandle: Page | null = null;
  
  private config: ImageManagementWorkflowConfig = {
    retryAttempts: 3,
    timeouts: {
      navigation: 30000,
      operation: 15000,
      upload: 20000,
    },
    performanceMode: true,
  };

  constructor(private page: Page) {
    this.authPage = new BiNDupAuthPage(page);
  }

  async initializeWorkflow(credentials: UserCredentials = TestUsers.VALID_USER): Promise<void> {
    TestLogger.logPhase('WORKFLOW INITIALIZATION', 'Starting Image Management workflow');
    
    await executeWithRetry(
      async () => {
        // Authenticate and launch BiNDup
        await this.authPage.navigateToAuth();
        await this.authPage.login(credentials.email, credentials.password);
        
        this.editorPageHandle = await this.authPage.launchBiNDup();
        
        // Initialize Image Management page object
        this.imageManagementPage = new BiNDupImageManagementPage(this.editorPageHandle);
        
        // Handle initial popups
        await this.handleInitialSetup();
        
        TestLogger.logStep('Image Management workflow initialized successfully', 'success');
      },
      'Initialize Image Management Workflow',
      this.config.retryAttempts
    );
  }

  async executeCompleteImageFlow(imagePath: string, newName?: string): Promise<ImageOperationResult> {
    TestLogger.logPhase('COMPLETE IMAGE FLOW', 'Executing upload → rename → edit → delete flow');
    
    const startTime = Date.now();
    
    try {
      // Step 1: Open Image Management
      await this.imageManagementPage.openImageManagement();
      
      // Step 2: Upload Image
      const imageId = await this.imageManagementPage.uploadImage(imagePath);
      
      // Step 3: Rename Image (if new name provided)
      if (newName) {
        await this.imageManagementPage.renameImage(imageId, newName);
      }
      
      // Step 4: Edit Image
      await this.imageManagementPage.editImage(imageId);
      
      // Step 5: Delete Image (cleanup)
      await this.imageManagementPage.deleteImage(imageId);
      
      const duration = Date.now() - startTime;
      
      TestLogger.logPhase('COMPLETE IMAGE FLOW', `Flow completed successfully in ${duration}ms`);
      
      return {
        success: true,
        imageId,
        duration,
      };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : String(error);
      
      TestLogger.logStep(`Complete image flow failed: ${errorMessage}`, 'error');
      
      return {
        success: false,
        error: errorMessage,
        duration,
      };
    }
  }

  async executeBulkImageOperations(imagePaths: string[], operations: ('upload' | 'edit' | 'delete')[]): Promise<ImageOperationResult[]> {
    TestLogger.logPhase('BULK OPERATIONS', `Processing ${imagePaths.length} images with operations: ${operations.join(', ')}`);
    
    const results: ImageOperationResult[] = [];
    const uploadedImages: string[] = [];
    
    try {
      await this.imageManagementPage.openImageManagement();
      
      // Upload phase
      if (operations.includes('upload')) {
        TestLogger.logStep('Bulk upload phase', 'start');
        for (const imagePath of imagePaths) {
          try {
            const imageId = await this.imageManagementPage.uploadImage(imagePath);
            uploadedImages.push(imageId);
            results.push({ success: true, imageId });
            TestLogger.logStep(`Uploaded: ${imagePath} → ${imageId}`, 'success');
          } catch (error) {
            results.push({ success: false, error: String(error) });
            TestLogger.logStep(`Upload failed: ${imagePath}`, 'error');
          }
        }
      }
      
      // Edit phase
      if (operations.includes('edit')) {
        TestLogger.logStep('Bulk edit phase', 'start');
        for (const imageId of uploadedImages) {
          try {
            await this.imageManagementPage.editImage(imageId);
            TestLogger.logStep(`Edited: ${imageId}`, 'success');
          } catch (error) {
            TestLogger.logStep(`Edit failed: ${imageId}`, 'error');
          }
        }
      }
      
      // Delete phase (cleanup)
      if (operations.includes('delete')) {
        TestLogger.logStep('Bulk delete phase', 'start');
        for (const imageId of uploadedImages) {
          try {
            await this.imageManagementPage.deleteImage(imageId);
            TestLogger.logStep(`Deleted: ${imageId}`, 'success');
          } catch (error) {
            TestLogger.logStep(`Delete failed: ${imageId}`, 'error');
          }
        }
      }
      
      TestLogger.logPhase('BULK OPERATIONS', 'Bulk operations completed');
      
    } catch (error) {
      TestLogger.logStep(`Bulk operations failed: ${error}`, 'error');
    }
    
    return results;
  }

  async executeImageUploadOnly(imagePath: string): Promise<ImageOperationResult> {
    TestLogger.logStep('Upload-only operation', 'start');
    
    const startTime = Date.now();
    
    try {
      await this.imageManagementPage.openImageManagement();
      const imageId = await this.imageManagementPage.uploadImage(imagePath);
      
      const duration = Date.now() - startTime;
      
      return {
        success: true,
        imageId,
        duration,
      };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      return {
        success: false,
        error: String(error),
        duration,
      };
    }
  }

  async executeImageEditOnly(imageId: string): Promise<ImageOperationResult> {
    TestLogger.logStep('Edit-only operation', 'start');
    
    const startTime = Date.now();
    
    try {
      await this.imageManagementPage.openImageManagement();
      await this.imageManagementPage.editImage(imageId);
      
      const duration = Date.now() - startTime;
      
      return {
        success: true,
        imageId,
        duration,
      };
      
    } catch (error) {
      const duration = Date.now() - startTime;
      
      return {
        success: false,
        error: String(error),
        duration,
      };
    }
  }

  async cleanupTestImages(imageIds: string[]): Promise<void> {
    TestLogger.logStep('Cleaning up test images', 'start');
    
    try {
      await this.imageManagementPage.openImageManagement();
      
      for (const imageId of imageIds) {
        try {
          await this.imageManagementPage.deleteImage(imageId);
          TestLogger.logStep(`Cleaned up image: ${imageId}`, 'success');
        } catch (error) {
          TestLogger.logStep(`Cleanup failed for: ${imageId}`, 'warning');
        }
      }
      
      TestLogger.logStep('Test image cleanup completed', 'success');
      
    } catch (error) {
      TestLogger.logStep(`Cleanup error: ${error}`, 'warning');
    }
  }

  async validateImageManagementHealth(): Promise<boolean> {
    TestLogger.logStep('Validating Image Management health', 'start');
    
    try {
      // Check if we can open image management
      await this.imageManagementPage.openImageManagement();
      
      // Check if basic functionality is available
      const testResult = await this.executeImageUploadOnly('test-data/images/Image-Editor-Test-Sample.png');
      
      if (testResult.success && testResult.imageId) {
        // Clean up test image
        await this.imageManagementPage.deleteImage(testResult.imageId);
        TestLogger.logStep('Image Management health check PASSED', 'success');
        return true;
      } else {
        TestLogger.logStep('Image Management health check FAILED', 'error');
        return false;
      }
      
    } catch (error) {
      TestLogger.logStep(`Health check error: ${error}`, 'error');
      return false;
    }
  }

  async getWorkflowMetrics(): Promise<{
    totalOperations: number;
    successfulOperations: number;
    failedOperations: number;
    averageDuration: number;
  }> {
    // This would integrate with TestMetrics to provide workflow-level metrics
    return {
      totalOperations: 0,
      successfulOperations: 0,
      failedOperations: 0,
      averageDuration: 0,
    };
  }

  async cleanup(): Promise<void> {
    TestLogger.logStep('Cleaning up workflow resources', 'start');
    
    try {
      if (this.editorPageHandle && !this.editorPageHandle.isClosed()) {
        await this.editorPageHandle.close();
      }
      TestLogger.logStep('Workflow cleanup completed', 'success');
    } catch (error) {
      TestLogger.logStep(`Cleanup warning: ${error}`, 'warning');
    }
  }

  // Private helper methods
  private async handleInitialSetup(): Promise<void> {
    if (!this.editorPageHandle) {
      throw new Error('Editor page not initialized');
    }

    // Handle Start Guide popup
    const popupSelectors = [
      '#button-1014',
      '#button-1031',
      'button:has-text("閉じる")',
      '.x-tool-close'
    ];

    for (const selector of popupSelectors) {
      try {
        const popup = this.editorPageHandle.locator(selector);
        if (await popup.isVisible({ timeout: 5000 })) {
          await popup.click();
          TestLogger.logStep(`Initial popup closed: ${selector}`, 'success');
          await this.editorPageHandle.waitForTimeout(2000);
          break;
        }
      } catch (error) {
        continue;
      }
    }
  }

  // Getters for accessing page objects
  get imagePage(): BiNDupImageManagementPage {
    if (!this.imageManagementPage) {
      throw new Error('Image Management page not initialized. Call initializeWorkflow() first.');
    }
    return this.imageManagementPage;
  }

  get editorPage(): Page {
    if (!this.editorPageHandle) {
      throw new Error('Editor page not initialized. Call initializeWorkflow() first.');
    }
    return this.editorPageHandle;
  }
}

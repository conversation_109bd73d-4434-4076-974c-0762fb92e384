#!/usr/bin/env node

/**
 * Simple test script to verify the MCP server functionality
 * This script tests the core features without requiring a full MCP client
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class MCPServerTester {
  constructor() {
    this.serverProcess = null;
  }

  async testServer() {
    console.log('🚀 Starting Playwright MCP Server Test...\n');

    try {
      // Test 1: Server startup
      console.log('📋 Test 1: Server Startup');
      await this.testServerStartup();
      console.log('✅ Server startup test passed\n');

      // Test 2: Tool listing
      console.log('📋 Test 2: Tool Listing');
      await this.testToolListing();
      console.log('✅ Tool listing test passed\n');

      // Test 3: Browser initialization
      console.log('📋 Test 3: Browser Initialization');
      await this.testBrowserInit();
      console.log('✅ Browser initialization test passed\n');

      // Test 4: Selector analysis
      console.log('📋 Test 4: Selector Analysis');
      await this.testSelectorAnalysis();
      console.log('✅ Selector analysis test passed\n');

      // Test 5: Smart loading
      console.log('📋 Test 5: Smart Loading');
      await this.testSmartLoading();
      console.log('✅ Smart loading test passed\n');

      console.log('🎉 All tests passed! MCP Server is working correctly.');

    } catch (error) {
      console.error('❌ Test failed:', error.message);
      process.exit(1);
    } finally {
      await this.cleanup();
    }
  }

  async testServerStartup() {
    // Build the server first
    console.log('Building server...');
    await this.runCommand('npm', ['run', 'build']);
    console.log('Server built successfully');
  }

  async testToolListing() {
    // Test that we can list tools
    const message = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/list',
      params: {}
    };

    const response = await this.sendMCPMessage(message);
    
    if (!response.result || !response.result.tools) {
      throw new Error('Failed to list tools');
    }

    const tools = response.result.tools;
    const expectedTools = [
      'playwright-analyze-selectors',
      'playwright-smart-wait',
      'playwright-debug-flow',
      'playwright-bindui-optimize',
      'playwright-init-browser',
      'playwright-close-browser'
    ];

    for (const expectedTool of expectedTools) {
      const found = tools.find(tool => tool.name === expectedTool);
      if (!found) {
        throw new Error(`Tool ${expectedTool} not found`);
      }
    }

    console.log(`Found ${tools.length} tools: ${tools.map(t => t.name).join(', ')}`);
  }

  async testBrowserInit() {
    const message = {
      jsonrpc: '2.0',
      id: 2,
      method: 'tools/call',
      params: {
        name: 'playwright-init-browser',
        arguments: {
          headless: true,
          url: 'https://example.com'
        }
      }
    };

    const response = await this.sendMCPMessage(message);
    
    if (!response.result) {
      throw new Error('Failed to initialize browser');
    }

    const result = JSON.parse(response.result.content[0].text);
    if (!result.success) {
      throw new Error('Browser initialization reported failure');
    }

    console.log('Browser initialized successfully');
  }

  async testSelectorAnalysis() {
    const message = {
      jsonrpc: '2.0',
      id: 3,
      method: 'tools/call',
      params: {
        name: 'playwright-analyze-selectors',
        arguments: {
          analysisDepth: 'basic',
          url: 'https://example.com'
        }
      }
    };

    const response = await this.sendMCPMessage(message);
    
    if (!response.result) {
      throw new Error('Failed to analyze selectors');
    }

    const result = JSON.parse(response.result.content[0].text);
    if (!result.summary) {
      throw new Error('Selector analysis missing summary');
    }

    console.log(`Analyzed ${result.summary.totalElements} elements`);
  }

  async testSmartLoading() {
    const message = {
      jsonrpc: '2.0',
      id: 4,
      method: 'tools/call',
      params: {
        name: 'playwright-smart-wait',
        arguments: {
          operation: 'test-operation',
          context: 'test-context',
          strategy: 'intelligent',
          maxWait: 5000
        }
      }
    };

    const response = await this.sendMCPMessage(message);
    
    if (!response.result) {
      throw new Error('Failed to test smart loading');
    }

    const result = JSON.parse(response.result.content[0].text);
    if (typeof result.success !== 'boolean') {
      throw new Error('Smart loading missing success indicator');
    }

    console.log(`Smart loading completed in ${result.duration}ms`);
  }

  async sendMCPMessage(message) {
    return new Promise((resolve, reject) => {
      const serverPath = join(__dirname, 'dist', 'index.js');
      const server = spawn('node', [serverPath], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      let responseData = '';
      let errorData = '';

      server.stdout.on('data', (data) => {
        responseData += data.toString();
      });

      server.stderr.on('data', (data) => {
        errorData += data.toString();
      });

      server.on('close', (code) => {
        if (code !== 0) {
          reject(new Error(`Server exited with code ${code}: ${errorData}`));
          return;
        }

        try {
          // Parse the JSON-RPC response
          const lines = responseData.trim().split('\n');
          const lastLine = lines[lines.length - 1];
          const response = JSON.parse(lastLine);
          resolve(response);
        } catch (error) {
          reject(new Error(`Failed to parse response: ${error.message}\nResponse: ${responseData}`));
        }
      });

      // Send the message
      server.stdin.write(JSON.stringify(message) + '\n');
      server.stdin.end();

      // Timeout after 10 seconds
      setTimeout(() => {
        server.kill();
        reject(new Error('Server response timeout'));
      }, 10000);
    });
  }

  async runCommand(command, args) {
    return new Promise((resolve, reject) => {
      const process = spawn(command, args, {
        cwd: __dirname,
        stdio: 'inherit'
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Command ${command} ${args.join(' ')} failed with code ${code}`));
        }
      });
    });
  }

  async cleanup() {
    if (this.serverProcess) {
      this.serverProcess.kill();
    }

    // Close any browsers that might be open
    try {
      const message = {
        jsonrpc: '2.0',
        id: 999,
        method: 'tools/call',
        params: {
          name: 'playwright-close-browser',
          arguments: {}
        }
      };
      await this.sendMCPMessage(message);
    } catch (error) {
      // Ignore cleanup errors
    }
  }
}

// Run the tests
const tester = new MCPServerTester();
tester.testServer().catch(console.error);

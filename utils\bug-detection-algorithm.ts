/**
 * Bug Detection Algorithm for BiNDup Test Automation
 * Comprehensive system to detect regressions and behavioral changes in new builds
 */

// Use console.log for now since TestLogger import path varies
// const { TestLogger } = require('./test-logger');

export interface TestResult {
  testName: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  errors: string[];
  expectedBehaviors: BehaviorCheck[];
  actualBehaviors: BehaviorCheck[];
  timestamp: string;
  buildVersion?: string;
}

export interface BehaviorCheck {
  name: string;
  expected: any;
  actual: any;
  status: 'pass' | 'fail' | 'warning';
  critical: boolean;
}

export interface BuildComparison {
  previousBuild: string;
  currentBuild: string;
  regressions: Regression[];
  improvements: Improvement[];
  newBehaviors: BehaviorCheck[];
  overallStatus: 'stable' | 'regression' | 'improvement' | 'critical';
}

export interface Regression {
  testName: string;
  behaviorName: string;
  previousValue: any;
  currentValue: any;
  severity: 'low' | 'medium' | 'high' | 'critical';
  impact: string;
}

export interface Improvement {
  testName: string;
  behaviorName: string;
  previousValue: any;
  currentValue: any;
  benefit: string;
}

/**
 * Bug Detection Algorithm Class
 */
export class BugDetectionAlgorithm {
  private baselineResults: Map<string, TestResult> = new Map();
  private currentResults: Map<string, TestResult> = new Map();
  private behaviorPatterns: Map<string, BehaviorPattern> = new Map();

  constructor() {
    this.initializeBehaviorPatterns();
    console.log('🛡️ Bug Detection Algorithm initialized');
  }

  /**
   * Record baseline test results (known good state)
   */
  recordBaseline(testResult: TestResult): void {
    this.baselineResults.set(testResult.testName, testResult);
    console.log(`📊 Baseline recorded for: ${testResult.testName}`);
  }

  /**
   * Analyze current test results against baseline
   */
  analyzeTestResult(testResult: TestResult): BuildComparison {
    console.log(`🔍 Analyzing test result: ${testResult.testName}`);

    const baseline = this.baselineResults.get(testResult.testName);
    if (!baseline) {
      console.log(`⚠️ No baseline found for: ${testResult.testName}`);
      return this.createNewTestAnalysis(testResult);
    }

    return this.compareWithBaseline(baseline, testResult);
  }

  /**
   * Detect specific behavior regressions
   */
  detectBehaviorRegressions(baseline: TestResult, current: TestResult): Regression[] {
    const regressions: Regression[] = [];

    // Check critical behaviors
    const criticalChecks = [
      this.checkTestStatus(baseline, current),
      this.checkDurationRegression(baseline, current),
      this.checkErrorPatterns(baseline, current),
      this.checkFunctionalBehaviors(baseline, current),
      this.checkUIInteractions(baseline, current),
      this.checkDataIntegrity(baseline, current)
    ];

    criticalChecks.forEach(check => {
      if (check) regressions.push(check);
    });

    return regressions;
  }

  /**
   * Check if test status has regressed
   */
  private checkTestStatus(baseline: TestResult, current: TestResult): Regression | null {
    if (baseline.status === 'passed' && current.status !== 'passed') {
      return {
        testName: current.testName,
        behaviorName: 'Test Status',
        previousValue: baseline.status,
        currentValue: current.status,
        severity: 'critical',
        impact: 'Test that was passing is now failing - critical regression'
      };
    }
    return null;
  }

  /**
   * Check for significant duration regressions
   */
  private checkDurationRegression(baseline: TestResult, current: TestResult): Regression | null {
    const durationIncrease = (current.duration - baseline.duration) / baseline.duration;
    
    if (durationIncrease > 0.5) { // 50% slower
      return {
        testName: current.testName,
        behaviorName: 'Test Duration',
        previousValue: `${baseline.duration}ms`,
        currentValue: `${current.duration}ms`,
        severity: 'medium',
        impact: `Test is ${Math.round(durationIncrease * 100)}% slower than baseline`
      };
    }
    return null;
  }

  /**
   * Check for new error patterns
   */
  private checkErrorPatterns(baseline: TestResult, current: TestResult): Regression | null {
    const newErrors = current.errors.filter(error => !baseline.errors.includes(error));
    
    if (newErrors.length > 0) {
      return {
        testName: current.testName,
        behaviorName: 'Error Patterns',
        previousValue: baseline.errors,
        currentValue: current.errors,
        severity: 'high',
        impact: `New errors detected: ${newErrors.join(', ')}`
      };
    }
    return null;
  }

  /**
   * Check functional behavior changes
   */
  private checkFunctionalBehaviors(baseline: TestResult, current: TestResult): Regression | null {
    const functionalChecks = [
      'Page Navigation Success',
      'Element Interaction Success',
      'Data Persistence',
      'UI State Consistency',
      'Loading State Handling'
    ];

    for (const checkName of functionalChecks) {
      const baselineBehavior = baseline.expectedBehaviors.find(b => b.name === checkName);
      const currentBehavior = current.actualBehaviors.find(b => b.name === checkName);

      if (baselineBehavior && currentBehavior) {
        if (baselineBehavior.status === 'pass' && currentBehavior.status !== 'pass') {
          return {
            testName: current.testName,
            behaviorName: checkName,
            previousValue: baselineBehavior.actual,
            currentValue: currentBehavior.actual,
            severity: currentBehavior.critical ? 'critical' : 'high',
            impact: `Functional behavior regression in ${checkName}`
          };
        }
      }
    }
    return null;
  }

  /**
   * Check UI interaction changes
   */
  private checkUIInteractions(baseline: TestResult, current: TestResult): Regression | null {
    const uiChecks = [
      'Button Click Success',
      'Form Input Success',
      'Hover Actions',
      'Popup Handling',
      'Menu Navigation'
    ];

    for (const checkName of uiChecks) {
      const baselineBehavior = baseline.expectedBehaviors.find(b => b.name === checkName);
      const currentBehavior = current.actualBehaviors.find(b => b.name === checkName);

      if (baselineBehavior && currentBehavior) {
        if (JSON.stringify(baselineBehavior.actual) !== JSON.stringify(currentBehavior.actual)) {
          return {
            testName: current.testName,
            behaviorName: checkName,
            previousValue: baselineBehavior.actual,
            currentValue: currentBehavior.actual,
            severity: 'medium',
            impact: `UI interaction behavior changed in ${checkName}`
          };
        }
      }
    }
    return null;
  }

  /**
   * Check data integrity
   */
  private checkDataIntegrity(baseline: TestResult, current: TestResult): Regression | null {
    const dataChecks = [
      'Data Creation Success',
      'Data Modification Success',
      'Data Deletion Success',
      'Data Validation Success'
    ];

    for (const checkName of dataChecks) {
      const baselineBehavior = baseline.expectedBehaviors.find(b => b.name === checkName);
      const currentBehavior = current.actualBehaviors.find(b => b.name === checkName);

      if (baselineBehavior && currentBehavior) {
        if (baselineBehavior.status === 'pass' && currentBehavior.status !== 'pass') {
          return {
            testName: current.testName,
            behaviorName: checkName,
            previousValue: baselineBehavior.actual,
            currentValue: currentBehavior.actual,
            severity: 'critical',
            impact: `Data integrity issue detected in ${checkName}`
          };
        }
      }
    }
    return null;
  }

  /**
   * Generate comprehensive bug report
   */
  generateBugReport(comparison: BuildComparison): string {
    let report = `
🛡️ **BUG DETECTION REPORT**
=====================================

📊 **Build Comparison:**
- Previous Build: ${comparison.previousBuild}
- Current Build: ${comparison.currentBuild}
- Overall Status: ${comparison.overallStatus.toUpperCase()}

`;

    if (comparison.regressions.length > 0) {
      report += `
❌ **REGRESSIONS DETECTED (${comparison.regressions.length}):**
`;
      comparison.regressions.forEach((regression, index) => {
        report += `
${index + 1}. **${regression.behaviorName}** (${regression.severity.toUpperCase()})
   Test: ${regression.testName}
   Previous: ${JSON.stringify(regression.previousValue)}
   Current: ${JSON.stringify(regression.currentValue)}
   Impact: ${regression.impact}
`;
      });
    }

    if (comparison.improvements.length > 0) {
      report += `
✅ **IMPROVEMENTS DETECTED (${comparison.improvements.length}):**
`;
      comparison.improvements.forEach((improvement, index) => {
        report += `
${index + 1}. **${improvement.behaviorName}**
   Test: ${improvement.testName}
   Previous: ${JSON.stringify(improvement.previousValue)}
   Current: ${JSON.stringify(improvement.currentValue)}
   Benefit: ${improvement.benefit}
`;
      });
    }

    if (comparison.newBehaviors.length > 0) {
      report += `
🆕 **NEW BEHAVIORS DETECTED (${comparison.newBehaviors.length}):**
`;
      comparison.newBehaviors.forEach((behavior, index) => {
        report += `
${index + 1}. **${behavior.name}** (${behavior.status.toUpperCase()})
   Expected: ${JSON.stringify(behavior.expected)}
   Actual: ${JSON.stringify(behavior.actual)}
   Critical: ${behavior.critical ? 'YES' : 'NO'}
`;
      });
    }

    report += `
📋 **RECOMMENDATIONS:**
`;

    if (comparison.overallStatus === 'critical') {
      report += `
🚨 **CRITICAL ISSUES DETECTED** - DO NOT DEPLOY
- Immediate investigation required
- Rollback recommended
- Contact development team
`;
    } else if (comparison.overallStatus === 'regression') {
      report += `
⚠️ **REGRESSIONS DETECTED** - REVIEW REQUIRED
- Investigate regressions before deployment
- Consider additional testing
- Monitor closely in staging
`;
    } else if (comparison.overallStatus === 'stable') {
      report += `
✅ **BUILD IS STABLE** - SAFE TO DEPLOY
- No critical regressions detected
- Performance within acceptable range
- All functional behaviors maintained
`;
    }

    return report;
  }

  /**
   * Initialize behavior patterns for detection
   */
  private initializeBehaviorPatterns(): void {
    // Define expected behavior patterns for each test type
    this.behaviorPatterns.set('SET-01', {
      criticalBehaviors: ['Block Addition Success', 'UI Responsiveness', 'Data Persistence'],
      performanceThresholds: { maxDuration: 120000, warningDuration: 90000 },
      errorPatterns: ['Target page, context or browser has been closed']
    });

    this.behaviorPatterns.set('SET-02-A', {
      criticalBehaviors: ['Page Duplication Success', 'Navigation Success', 'Data Integrity'],
      performanceThresholds: { maxDuration: 150000, warningDuration: 120000 },
      errorPatterns: ['Duplication failed', 'Page not found']
    });

    // Add more patterns as needed
  }

  /**
   * Compare current result with baseline
   */
  private compareWithBaseline(baseline: TestResult, current: TestResult): BuildComparison {
    const regressions = this.detectBehaviorRegressions(baseline, current);
    const improvements = this.detectImprovements(baseline, current);
    const newBehaviors = this.detectNewBehaviors(baseline, current);

    let overallStatus: 'stable' | 'regression' | 'improvement' | 'critical' = 'stable';

    if (regressions.some(r => r.severity === 'critical')) {
      overallStatus = 'critical';
    } else if (regressions.length > 0) {
      overallStatus = 'regression';
    } else if (improvements.length > 0) {
      overallStatus = 'improvement';
    }

    return {
      previousBuild: baseline.buildVersion || 'baseline',
      currentBuild: current.buildVersion || 'current',
      regressions,
      improvements,
      newBehaviors,
      overallStatus
    };
  }

  /**
   * Create analysis for new test (no baseline)
   */
  private createNewTestAnalysis(testResult: TestResult): BuildComparison {
    return {
      previousBuild: 'none',
      currentBuild: testResult.buildVersion || 'current',
      regressions: [],
      improvements: [],
      newBehaviors: testResult.actualBehaviors,
      overallStatus: testResult.status === 'passed' ? 'stable' : 'regression'
    };
  }

  /**
   * Detect improvements
   */
  private detectImprovements(baseline: TestResult, current: TestResult): Improvement[] {
    const improvements: Improvement[] = [];

    // Check for performance improvements
    if (current.duration < baseline.duration * 0.8) { // 20% faster
      improvements.push({
        testName: current.testName,
        behaviorName: 'Performance',
        previousValue: `${baseline.duration}ms`,
        currentValue: `${current.duration}ms`,
        benefit: `Test is ${Math.round((1 - current.duration / baseline.duration) * 100)}% faster`
      });
    }

    // Check for error reductions
    if (current.errors.length < baseline.errors.length) {
      improvements.push({
        testName: current.testName,
        behaviorName: 'Error Reduction',
        previousValue: baseline.errors.length,
        currentValue: current.errors.length,
        benefit: `Reduced errors from ${baseline.errors.length} to ${current.errors.length}`
      });
    }

    return improvements;
  }

  /**
   * Detect new behaviors
   */
  private detectNewBehaviors(baseline: TestResult, current: TestResult): BehaviorCheck[] {
    return current.actualBehaviors.filter(currentBehavior => 
      !baseline.actualBehaviors.some(baselineBehavior => 
        baselineBehavior.name === currentBehavior.name
      )
    );
  }
}

interface BehaviorPattern {
  criticalBehaviors: string[];
  performanceThresholds: {
    maxDuration: number;
    warningDuration: number;
  };
  errorPatterns: string[];
}

// Export for CommonJS
module.exports = { BugDetectionAlgorithm };

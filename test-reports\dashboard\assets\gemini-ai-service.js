/**
 * 🤖 Gemini AI Integration Service
 * Advanced AI analysis for test results using Google Gemini API
 */

class GeminiAIService {
    constructor() {
        this.apiKey = null;
        this.isEnabled = localStorage.getItem('gemini-ai-enabled') === 'true';
        this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent';
        this.chatModel = 'gemini-pro';
        this.analysisCache = new Map();
        
        this.init();
    }

    init() {
        // Load API key from localStorage if available
        this.apiKey = localStorage.getItem('gemini-api-key');
        
        // Set up UI elements
        this.setupAIToggle();
        this.setupAPIKeyInput();
        this.setupChatInterface();
    }

    setupAIToggle() {
        const toggleContainer = document.createElement('div');
        toggleContainer.className = 'fixed top-20 right-4 z-50';
        toggleContainer.innerHTML = `
            <div class="glass-morphism rounded-2xl p-4 border border-white/20">
                <div class="flex items-center space-x-3">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-brain text-purple-400"></i>
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">AI Analysis</span>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="ai-toggle" class="sr-only peer" ${this.isEnabled ? 'checked' : ''}>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
                    </label>
                </div>
                <div id="ai-status" class="mt-2 text-xs ${this.isEnabled ? 'text-green-600' : 'text-gray-500'}">
                    ${this.isEnabled ? '🟢 AI Analysis Enabled' : '⚪ AI Analysis Disabled'}
                </div>
            </div>
        `;
        
        document.body.appendChild(toggleContainer);
        
        // Add event listener
        const toggle = document.getElementById('ai-toggle');
        toggle.addEventListener('change', (e) => {
            this.toggleAI(e.target.checked);
        });
    }

    setupAPIKeyInput() {
        const keyInputContainer = document.createElement('div');
        keyInputContainer.id = 'api-key-modal';
        keyInputContainer.className = 'fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden flex items-center justify-center';
        keyInputContainer.innerHTML = `
            <div class="glass-morphism rounded-3xl p-8 max-w-md w-full mx-4 border border-white/20">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-key text-purple-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Gemini AI Configuration</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">Enter your Google Gemini API key to enable AI analysis</p>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">API Key</label>
                        <input type="password" id="gemini-api-key" placeholder="Enter your Gemini API key" 
                               class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500">
                    </div>
                    
                    <div class="flex space-x-3">
                        <button id="save-api-key" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-xl font-medium transition-colors">
                            <i class="fas fa-save mr-2"></i>Save & Enable
                        </button>
                        <button id="cancel-api-key" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-3 rounded-xl font-medium transition-colors">
                            Cancel
                        </button>
                    </div>
                    
                    <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
                        <a href="https://makersuite.google.com/app/apikey" target="_blank" class="text-purple-400 hover:text-purple-300">
                            Get your free Gemini API key →
                        </a>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(keyInputContainer);
        
        // Add event listeners
        document.getElementById('save-api-key').addEventListener('click', () => this.saveAPIKey());
        document.getElementById('cancel-api-key').addEventListener('click', () => this.hideAPIKeyModal());
    }

    setupChatInterface() {
        const chatContainer = document.createElement('div');
        chatContainer.id = 'ai-chat-modal';
        chatContainer.className = 'fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden';
        chatContainer.innerHTML = `
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="glass-morphism rounded-3xl w-full max-w-4xl max-h-[90vh] overflow-hidden border border-white/20">
                    <!-- Chat Header -->
                    <div class="flex items-center justify-between p-6 border-b border-white/20">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-purple-500/20 rounded-full flex items-center justify-center">
                                <i class="fas fa-robot text-purple-400"></i>
                            </div>
                            <div>
                                <h3 class="text-lg font-bold text-gray-900 dark:text-white">Gemini AI Assistant</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">Intelligent test analysis and insights</p>
                            </div>
                        </div>
                        <button id="close-ai-chat" class="w-10 h-10 bg-red-500/20 rounded-full flex items-center justify-center hover:bg-red-500/30 transition-colors">
                            <i class="fas fa-times text-red-400"></i>
                        </button>
                    </div>
                    
                    <!-- Chat Messages -->
                    <div id="ai-chat-messages" class="h-96 overflow-y-auto p-6 space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-robot text-purple-400 text-sm"></i>
                            </div>
                            <div class="bg-white/10 rounded-2xl p-4 max-w-md">
                                <p class="text-gray-700 dark:text-gray-300 text-sm">
                                    Hello! I'm your AI assistant. I can analyze your test results, provide insights, and answer questions about your testing data. How can I help you today?
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Chat Input -->
                    <div class="p-6 border-t border-white/20">
                        <div class="flex space-x-3">
                            <input type="text" id="ai-chat-input" placeholder="Ask me about your test results..." 
                                   class="flex-1 px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500">
                            <button id="send-ai-message" class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-xl font-medium transition-colors">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                        <div class="flex items-center justify-between mt-3">
                            <div class="flex space-x-2">
                                <button class="ai-quick-question px-3 py-1 bg-white/10 rounded-full text-xs text-gray-600 dark:text-gray-400 hover:bg-white/20 transition-colors" data-question="Analyze my test failures">
                                    Analyze failures
                                </button>
                                <button class="ai-quick-question px-3 py-1 bg-white/10 rounded-full text-xs text-gray-600 dark:text-gray-400 hover:bg-white/20 transition-colors" data-question="What are the performance bottlenecks?">
                                    Performance issues
                                </button>
                                <button class="ai-quick-question px-3 py-1 bg-white/10 rounded-full text-xs text-gray-600 dark:text-gray-400 hover:bg-white/20 transition-colors" data-question="Suggest improvements">
                                    Improvements
                                </button>
                            </div>
                            <div id="ai-typing-indicator" class="hidden text-xs text-purple-400">
                                <i class="fas fa-circle animate-pulse"></i>
                                <i class="fas fa-circle animate-pulse" style="animation-delay: 0.2s"></i>
                                <i class="fas fa-circle animate-pulse" style="animation-delay: 0.4s"></i>
                                AI is thinking...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(chatContainer);
        
        // Add event listeners
        document.getElementById('close-ai-chat').addEventListener('click', () => this.hideChatModal());
        document.getElementById('send-ai-message').addEventListener('click', () => this.sendMessage());
        document.getElementById('ai-chat-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });
        
        // Quick question buttons
        document.querySelectorAll('.ai-quick-question').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const question = e.target.dataset.question;
                document.getElementById('ai-chat-input').value = question;
                this.sendMessage();
            });
        });
    }

    toggleAI(enabled) {
        this.isEnabled = enabled;
        localStorage.setItem('gemini-ai-enabled', enabled.toString());
        
        const statusEl = document.getElementById('ai-status');
        if (enabled) {
            if (!this.apiKey) {
                this.showAPIKeyModal();
                return;
            }
            statusEl.textContent = '🟢 AI Analysis Enabled';
            statusEl.className = 'mt-2 text-xs text-green-600';
            this.generateAIInsights();
        } else {
            statusEl.textContent = '⚪ AI Analysis Disabled';
            statusEl.className = 'mt-2 text-xs text-gray-500';
        }
    }

    showAPIKeyModal() {
        document.getElementById('api-key-modal').classList.remove('hidden');
    }

    hideAPIKeyModal() {
        document.getElementById('api-key-modal').classList.add('hidden');
        // Reset toggle if no API key
        if (!this.apiKey) {
            document.getElementById('ai-toggle').checked = false;
            this.isEnabled = false;
        }
    }

    saveAPIKey() {
        const apiKey = document.getElementById('gemini-api-key').value.trim();
        if (!apiKey) {
            this.showToast('Please enter a valid API key', 'error');
            return;
        }
        
        this.apiKey = apiKey;
        localStorage.setItem('gemini-api-key', apiKey);
        this.hideAPIKeyModal();
        
        // Enable AI and generate insights
        this.isEnabled = true;
        localStorage.setItem('gemini-ai-enabled', 'true');
        document.getElementById('ai-status').textContent = '🟢 AI Analysis Enabled';
        document.getElementById('ai-status').className = 'mt-2 text-xs text-green-600';
        
        this.showToast('Gemini AI enabled successfully!', 'success');
        this.generateAIInsights();
    }

    showChatModal() {
        if (!this.isEnabled || !this.apiKey) {
            this.showToast('Please enable AI analysis first', 'warning');
            return;
        }
        document.getElementById('ai-chat-modal').classList.remove('hidden');
    }

    hideChatModal() {
        document.getElementById('ai-chat-modal').classList.add('hidden');
    }

    async sendMessage() {
        const input = document.getElementById('ai-chat-input');
        const message = input.value.trim();
        if (!message) return;
        
        // Add user message to chat
        this.addMessageToChat(message, 'user');
        input.value = '';
        
        // Show typing indicator
        document.getElementById('ai-typing-indicator').classList.remove('hidden');
        
        try {
            const response = await this.queryGemini(message);
            this.addMessageToChat(response, 'ai');
        } catch (error) {
            this.addMessageToChat('Sorry, I encountered an error. Please check your API key and try again.', 'ai');
            console.error('Gemini API error:', error);
        }
        
        // Hide typing indicator
        document.getElementById('ai-typing-indicator').classList.add('hidden');
    }

    addMessageToChat(message, sender) {
        const messagesContainer = document.getElementById('ai-chat-messages');
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex items-start space-x-3';
        
        if (sender === 'user') {
            messageDiv.innerHTML = `
                <div class="flex-1"></div>
                <div class="bg-purple-600 rounded-2xl p-4 max-w-md">
                    <p class="text-white text-sm">${message}</p>
                </div>
                <div class="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-user text-blue-400 text-sm"></i>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center flex-shrink-0">
                    <i class="fas fa-robot text-purple-400 text-sm"></i>
                </div>
                <div class="bg-white/10 rounded-2xl p-4 max-w-md">
                    <p class="text-gray-700 dark:text-gray-300 text-sm">${message}</p>
                </div>
            `;
        }
        
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    async queryGemini(prompt, testData = null) {
        if (!this.apiKey) {
            throw new Error('No API key configured');
        }
        
        // Prepare context with test data if available
        let contextualPrompt = prompt;
        if (testData || window.dashboard?.data) {
            const data = testData || window.dashboard.data;
            contextualPrompt = `
Based on the following test data:
- Total Tests: ${data.summary.totalTests}
- Pass Rate: ${data.summary.passRate}%
- Failed Tests: ${data.summary.failedTests}
- Quality Score: ${data.summary.qualityScore}
- Test Suites: ${data.suites.map(s => `${s.name} (${s.metrics.passRate}%)`).join(', ')}

User Question: ${prompt}

Please provide a helpful, specific analysis based on this test data.
`;
        }
        
        const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                contents: [{
                    parts: [{
                        text: contextualPrompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.7,
                    topK: 40,
                    topP: 0.95,
                    maxOutputTokens: 1024,
                }
            })
        });
        
        if (!response.ok) {
            throw new Error(`Gemini API error: ${response.status}`);
        }
        
        const result = await response.json();
        return result.candidates[0].content.parts[0].text;
    }

    async generateAIInsights() {
        if (!this.isEnabled || !this.apiKey || !window.dashboard?.data) return;
        
        try {
            const testData = window.dashboard.data;
            const prompt = `
Analyze this test automation report and provide:
1. Key insights about test performance
2. Risk assessment and areas of concern
3. Specific recommendations for improvement
4. Predictions for future test runs

Test Data Summary:
- Total Tests: ${testData.summary.totalTests}
- Pass Rate: ${testData.summary.passRate}%
- Failed Tests: ${testData.summary.failedTests}
- Quality Score: ${testData.summary.qualityScore}
- Execution Time: ${Math.round(testData.summary.executionTime / 60000)} minutes

Test Suites Performance:
${testData.suites.map(suite => 
    `- ${suite.name}: ${suite.metrics.passRate}% (${suite.metrics.passedTests}/${suite.metrics.totalTests})`
).join('\n')}

Please provide actionable insights in a professional format.
`;
            
            const aiInsights = await this.queryGemini(prompt, testData);
            this.displayAIInsights(aiInsights);
            
        } catch (error) {
            console.error('Failed to generate AI insights:', error);
            this.showToast('Failed to generate AI insights. Please check your API key.', 'error');
        }
    }

    displayAIInsights(insights) {
        // Add AI insights to the dashboard
        const insightsSection = document.getElementById('modern-key-findings');
        if (insightsSection) {
            const aiInsightDiv = document.createElement('div');
            aiInsightDiv.className = 'mt-6 p-4 bg-purple-500/10 rounded-xl border border-purple-500/20';
            aiInsightDiv.innerHTML = `
                <div class="flex items-center space-x-2 mb-3">
                    <i class="fas fa-robot text-purple-400"></i>
                    <h5 class="font-semibold text-purple-600 dark:text-purple-400">AI Analysis</h5>
                </div>
                <div class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line">${insights}</div>
            `;
            insightsSection.appendChild(aiInsightDiv);
        }
    }

    showToast(message, type = 'info') {
        // Use the existing toast system from the main dashboard
        if (window.modernDashboard && window.modernDashboard.showToast) {
            window.modernDashboard.showToast(message, type);
        }
    }
}

// Initialize Gemini AI Service
window.geminiAI = new GeminiAIService();

{"name": "bindup-automation-tests", "version": "1.0.0", "description": "BiNDup Platform Automation Tests - Chrome & WebKit (GitHub Actions Enabled)", "main": "index.js", "scripts": {"test": "npx playwright test", "test:headed": "npx playwright test --headed", "test:chromium": "npx playwright test --project=chromium", "test:webkit": "npx playwright test --project=webkit", "test:both": "npx playwright test --project=chromium --project=webkit", "test:debug": "npx playwright test --debug", "test:ui": "npx playwright test --ui", "report": "npx playwright show-report", "install-browsers": "npx playwright install", "install-deps": "npx playwright install-deps", "test:ci": "npx playwright test --project=chromium --project=webkit --workers=2 --max-failures=10 --reporter=html,json,junit", "dashboard": "node scripts/generate-dashboard-report.js", "test:dashboard": "node scripts/run-tests-with-dashboard.js", "test:dashboard:webkit": "node scripts/run-tests-with-dashboard.js --webkit", "test:dashboard:both": "node scripts/run-tests-with-dashboard.js --both", "dashboard:only": "node scripts/run-tests-with-dashboard.js --skip-tests", "test:full-report": "npm run test && npm run dashboard && npm run report"}, "keywords": ["playwright", "testing", "automation", "bindup", "chrome", "webkit", "e2e", "site-creation", "image-crud"], "author": "WebLife Japan", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0", "@types/node": "^20.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/wl-tung/automation-test-bind.git"}, "bugs": {"url": "https://github.com/wl-tung/automation-test-bind/issues"}, "homepage": "https://github.com/wl-tung/automation-test-bind#readme"}
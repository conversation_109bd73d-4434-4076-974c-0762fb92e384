/**
 * 📊 Advanced Export Service
 * Professional export functionality for PDF, XLSX, CSV formats
 */

class AdvancedExportService {
    constructor() {
        this.exportFormats = ['pdf', 'xlsx', 'csv', 'json'];
        this.setupExportUI();
        this.loadExternalLibraries();
    }

    async loadExternalLibraries() {
        // Load jsPDF for PDF generation
        if (!window.jsPDF) {
            await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js');
        }
        
        // Load SheetJS for XLSX generation
        if (!window.XLSX) {
            await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js');
        }
        
        // Load html2canvas for PDF screenshots
        if (!window.html2canvas) {
            await this.loadScript('https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js');
        }
        
        console.log('📚 Export libraries loaded successfully');
    }

    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    setupExportUI() {
        // Replace the simple export button with advanced export dropdown
        const exportBtn = document.getElementById('export-btn');
        if (exportBtn) {
            exportBtn.innerHTML = `
                <i class="fas fa-download mr-2"></i>
                <span class="hidden sm:inline">Export</span>
                <i class="fas fa-chevron-down ml-1"></i>
            `;
            
            // Create dropdown menu
            const dropdown = document.createElement('div');
            dropdown.id = 'export-dropdown';
            dropdown.className = 'absolute top-full right-0 mt-2 w-48 glass-morphism rounded-xl border border-white/20 shadow-lg z-50 hidden';
            dropdown.innerHTML = `
                <div class="p-2">
                    <button class="export-option w-full text-left px-3 py-2 rounded-lg hover:bg-white/10 transition-colors flex items-center space-x-3" data-format="pdf">
                        <i class="fas fa-file-pdf text-red-400"></i>
                        <div>
                            <div class="font-medium text-gray-900 dark:text-white">PDF Report</div>
                            <div class="text-xs text-gray-500">Professional document</div>
                        </div>
                    </button>
                    <button class="export-option w-full text-left px-3 py-2 rounded-lg hover:bg-white/10 transition-colors flex items-center space-x-3" data-format="xlsx">
                        <i class="fas fa-file-excel text-green-400"></i>
                        <div>
                            <div class="font-medium text-gray-900 dark:text-white">Excel Spreadsheet</div>
                            <div class="text-xs text-gray-500">Data analysis ready</div>
                        </div>
                    </button>
                    <button class="export-option w-full text-left px-3 py-2 rounded-lg hover:bg-white/10 transition-colors flex items-center space-x-3" data-format="csv">
                        <i class="fas fa-file-csv text-blue-400"></i>
                        <div>
                            <div class="font-medium text-gray-900 dark:text-white">CSV Data</div>
                            <div class="text-xs text-gray-500">Raw data export</div>
                        </div>
                    </button>
                    <button class="export-option w-full text-left px-3 py-2 rounded-lg hover:bg-white/10 transition-colors flex items-center space-x-3" data-format="json">
                        <i class="fas fa-file-code text-purple-400"></i>
                        <div>
                            <div class="font-medium text-gray-900 dark:text-white">JSON Data</div>
                            <div class="text-xs text-gray-500">Complete dataset</div>
                        </div>
                    </button>
                </div>
            `;
            
            // Position dropdown relative to button
            exportBtn.style.position = 'relative';
            exportBtn.appendChild(dropdown);
            
            // Add event listeners
            exportBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleExportDropdown();
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', () => {
                this.hideExportDropdown();
            });
            
            // Add export option listeners
            document.querySelectorAll('.export-option').forEach(option => {
                option.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const format = e.currentTarget.dataset.format;
                    this.exportReport(format);
                    this.hideExportDropdown();
                });
            });
        }
    }

    toggleExportDropdown() {
        const dropdown = document.getElementById('export-dropdown');
        dropdown.classList.toggle('hidden');
    }

    hideExportDropdown() {
        const dropdown = document.getElementById('export-dropdown');
        if (dropdown) {
            dropdown.classList.add('hidden');
        }
    }

    async exportReport(format) {
        if (!window.dashboard?.data) {
            this.showToast('No data available for export', 'error');
            return;
        }

        this.showToast(`Generating ${format.toUpperCase()} report...`, 'info');

        try {
            switch (format) {
                case 'pdf':
                    await this.exportToPDF();
                    break;
                case 'xlsx':
                    await this.exportToXLSX();
                    break;
                case 'csv':
                    await this.exportToCSV();
                    break;
                case 'json':
                    await this.exportToJSON();
                    break;
                default:
                    throw new Error(`Unsupported format: ${format}`);
            }
            
            this.showToast(`${format.toUpperCase()} report exported successfully!`, 'success');
        } catch (error) {
            console.error(`Export error (${format}):`, error);
            this.showToast(`Failed to export ${format.toUpperCase()} report`, 'error');
        }
    }

    async exportToPDF() {
        const { jsPDF } = window.jsPDF;
        const doc = new jsPDF('p', 'mm', 'a4');
        const data = window.dashboard.data;
        
        // PDF Configuration
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        const margin = 20;
        let yPosition = margin;
        
        // Helper function to add new page if needed
        const checkPageBreak = (requiredHeight) => {
            if (yPosition + requiredHeight > pageHeight - margin) {
                doc.addPage();
                yPosition = margin;
            }
        };
        
        // Title and Header
        doc.setFontSize(24);
        doc.setTextColor(33, 150, 243); // Primary blue
        doc.text('BiNDup Test Intelligence Report', margin, yPosition);
        yPosition += 15;
        
        // Subtitle
        doc.setFontSize(12);
        doc.setTextColor(100, 100, 100);
        doc.text(`Generated on ${new Date().toLocaleDateString()} at ${new Date().toLocaleTimeString()}`, margin, yPosition);
        yPosition += 10;
        
        // Report ID
        doc.text(`Report ID: ${data.metadata.reportId}`, margin, yPosition);
        yPosition += 15;
        
        // Executive Summary
        checkPageBreak(40);
        doc.setFontSize(16);
        doc.setTextColor(0, 0, 0);
        doc.text('Executive Summary', margin, yPosition);
        yPosition += 10;
        
        doc.setFontSize(11);
        const summaryData = [
            ['Total Tests', data.summary.totalTests.toString()],
            ['Passed Tests', data.summary.passedTests.toString()],
            ['Failed Tests', data.summary.failedTests.toString()],
            ['Pass Rate', `${data.summary.passRate}%`],
            ['Quality Score', `${data.summary.qualityScore}/100`],
            ['Execution Time', `${Math.round(data.summary.executionTime / 60000)} minutes`],
            ['Risk Level', data.summary.riskLevel]
        ];
        
        summaryData.forEach(([label, value]) => {
            doc.text(`${label}:`, margin, yPosition);
            doc.text(value, margin + 50, yPosition);
            yPosition += 6;
        });
        
        yPosition += 10;
        
        // Test Suites Performance
        checkPageBreak(60);
        doc.setFontSize(16);
        doc.text('Test Suites Performance', margin, yPosition);
        yPosition += 10;
        
        doc.setFontSize(11);
        data.suites.forEach(suite => {
            checkPageBreak(25);
            
            // Suite name
            doc.setTextColor(33, 150, 243);
            doc.text(`${suite.icon} ${suite.name}`, margin, yPosition);
            yPosition += 6;
            
            doc.setTextColor(0, 0, 0);
            doc.text(`Description: ${suite.description}`, margin + 5, yPosition);
            yPosition += 5;
            doc.text(`Pass Rate: ${suite.metrics.passRate}% (${suite.metrics.passedTests}/${suite.metrics.totalTests})`, margin + 5, yPosition);
            yPosition += 5;
            doc.text(`Avg Execution Time: ${Math.round(suite.metrics.avgExecutionTime / 1000)}s`, margin + 5, yPosition);
            yPosition += 8;
        });
        
        // AI Insights (if available)
        if (data.insights) {
            checkPageBreak(40);
            doc.setFontSize(16);
            doc.setTextColor(156, 39, 176); // Purple for AI
            doc.text('🤖 AI-Powered Insights', margin, yPosition);
            yPosition += 10;
            
            doc.setFontSize(11);
            doc.setTextColor(0, 0, 0);
            
            // Key Findings
            doc.text('Key Findings:', margin, yPosition);
            yPosition += 6;
            data.insights.keyFindings.forEach(finding => {
                checkPageBreak(8);
                doc.text(`• ${finding}`, margin + 5, yPosition);
                yPosition += 5;
            });
            
            yPosition += 5;
            
            // Recommendations
            doc.text('Recommendations:', margin, yPosition);
            yPosition += 6;
            data.recommendations.forEach(rec => {
                checkPageBreak(12);
                doc.text(`• ${rec.title} (${rec.priority} Priority)`, margin + 5, yPosition);
                yPosition += 5;
                doc.text(`  ${rec.description}`, margin + 8, yPosition);
                yPosition += 7;
            });
        }
        
        // Footer
        const totalPages = doc.internal.getNumberOfPages();
        for (let i = 1; i <= totalPages; i++) {
            doc.setPage(i);
            doc.setFontSize(8);
            doc.setTextColor(150, 150, 150);
            doc.text(`Page ${i} of ${totalPages}`, pageWidth - margin - 20, pageHeight - 10);
            doc.text('BiNDup Test Intelligence Dashboard', margin, pageHeight - 10);
        }
        
        // Save the PDF
        const fileName = `bindup-test-report-${new Date().toISOString().split('T')[0]}.pdf`;
        doc.save(fileName);
    }

    async exportToXLSX() {
        const data = window.dashboard.data;
        const workbook = XLSX.utils.book_new();
        
        // Summary Sheet
        const summaryData = [
            ['Metric', 'Value'],
            ['Total Tests', data.summary.totalTests],
            ['Passed Tests', data.summary.passedTests],
            ['Failed Tests', data.summary.failedTests],
            ['Skipped Tests', data.summary.skippedTests],
            ['Pass Rate (%)', data.summary.passRate],
            ['Quality Score', data.summary.qualityScore],
            ['Execution Time (minutes)', Math.round(data.summary.executionTime / 60000)],
            ['Risk Level', data.summary.riskLevel],
            ['Environment', data.summary.environment],
            ['Build Version', data.summary.buildVersion],
            ['Generated At', new Date(data.metadata.generatedAt).toLocaleString()],
            ['Report ID', data.metadata.reportId]
        ];
        
        const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
        XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
        
        // Test Suites Sheet
        const suitesData = [
            ['Suite Name', 'Category', 'Total Tests', 'Passed', 'Failed', 'Pass Rate (%)', 'Avg Time (s)', 'Improvements']
        ];
        
        data.suites.forEach(suite => {
            suitesData.push([
                suite.name,
                suite.category,
                suite.metrics.totalTests,
                suite.metrics.passedTests,
                suite.metrics.failedTests,
                suite.metrics.passRate,
                Math.round(suite.metrics.avgExecutionTime / 1000),
                suite.metrics.improvementCount
            ]);
        });
        
        const suitesSheet = XLSX.utils.aoa_to_sheet(suitesData);
        XLSX.utils.book_append_sheet(workbook, suitesSheet, 'Test Suites');
        
        // Detailed Tests Sheet (if available)
        if (data.detailedTests) {
            const detailedData = [
                ['Test ID', 'Test Name', 'Suite', 'Status', 'Duration (ms)', 'Feature', 'Scenario']
            ];
            
            Object.entries(data.detailedTests).forEach(([category, tests]) => {
                tests.forEach(test => {
                    detailedData.push([
                        test.id,
                        test.name,
                        category,
                        test.status,
                        test.duration,
                        test.gherkin.feature,
                        test.gherkin.scenario
                    ]);
                });
            });
            
            const detailedSheet = XLSX.utils.aoa_to_sheet(detailedData);
            XLSX.utils.book_append_sheet(workbook, detailedSheet, 'Detailed Tests');
        }
        
        // Insights Sheet
        if (data.insights) {
            const insightsData = [
                ['Type', 'Content'],
                ['', ''],
                ['KEY FINDINGS', ''],
                ...data.insights.keyFindings.map(finding => ['Finding', finding]),
                ['', ''],
                ['RISK AREAS', ''],
                ...data.insights.riskAreas.map(risk => ['Risk', risk]),
                ['', ''],
                ['IMPROVEMENTS', ''],
                ...data.insights.improvements.map(improvement => ['Improvement', improvement]),
                ['', ''],
                ['RECOMMENDATIONS', ''],
                ...data.recommendations.map(rec => ['Recommendation', `${rec.title} (${rec.priority}): ${rec.description}`])
            ];
            
            const insightsSheet = XLSX.utils.aoa_to_sheet(insightsData);
            XLSX.utils.book_append_sheet(workbook, insightsSheet, 'Insights');
        }
        
        // Save the workbook
        const fileName = `bindup-test-report-${new Date().toISOString().split('T')[0]}.xlsx`;
        XLSX.writeFile(workbook, fileName);
    }

    async exportToCSV() {
        const data = window.dashboard.data;
        
        // Create comprehensive CSV data
        const csvData = [
            // Header
            ['BiNDup Test Intelligence Report'],
            [`Generated: ${new Date().toLocaleString()}`],
            [`Report ID: ${data.metadata.reportId}`],
            [''],
            
            // Summary
            ['EXECUTIVE SUMMARY'],
            ['Metric', 'Value'],
            ['Total Tests', data.summary.totalTests],
            ['Passed Tests', data.summary.passedTests],
            ['Failed Tests', data.summary.failedTests],
            ['Pass Rate (%)', data.summary.passRate],
            ['Quality Score', data.summary.qualityScore],
            ['Execution Time (minutes)', Math.round(data.summary.executionTime / 60000)],
            ['Risk Level', data.summary.riskLevel],
            [''],
            
            // Test Suites
            ['TEST SUITES PERFORMANCE'],
            ['Suite Name', 'Category', 'Total Tests', 'Passed', 'Failed', 'Pass Rate (%)', 'Avg Time (s)'],
            ...data.suites.map(suite => [
                suite.name,
                suite.category,
                suite.metrics.totalTests,
                suite.metrics.passedTests,
                suite.metrics.failedTests,
                suite.metrics.passRate,
                Math.round(suite.metrics.avgExecutionTime / 1000)
            ]),
            [''],
            
            // Key Findings
            ['KEY FINDINGS'],
            ...data.insights.keyFindings.map(finding => [finding]),
            [''],
            
            // Recommendations
            ['RECOMMENDATIONS'],
            ['Title', 'Priority', 'Description'],
            ...data.recommendations.map(rec => [rec.title, rec.priority, rec.description])
        ];
        
        // Convert to CSV string
        const csvContent = csvData.map(row => 
            row.map(cell => `"${String(cell).replace(/"/g, '""')}"`).join(',')
        ).join('\n');
        
        // Download CSV
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const link = document.createElement('a');
        const fileName = `bindup-test-report-${new Date().toISOString().split('T')[0]}.csv`;
        
        link.href = URL.createObjectURL(blob);
        link.download = fileName;
        link.click();
        
        URL.revokeObjectURL(link.href);
    }

    async exportToJSON() {
        const data = window.dashboard.data;
        
        // Enhanced JSON with export metadata
        const exportData = {
            ...data,
            exportMetadata: {
                exportedAt: new Date().toISOString(),
                exportFormat: 'JSON',
                exportVersion: '2.0.0',
                exportedBy: 'BiNDup Test Intelligence Dashboard'
            }
        };
        
        const jsonString = JSON.stringify(exportData, null, 2);
        const blob = new Blob([jsonString], { type: 'application/json' });
        const link = document.createElement('a');
        const fileName = `bindup-test-report-${new Date().toISOString().split('T')[0]}.json`;
        
        link.href = URL.createObjectURL(blob);
        link.download = fileName;
        link.click();
        
        URL.revokeObjectURL(link.href);
    }

    showToast(message, type = 'info') {
        // Use the existing toast system from the main dashboard
        if (window.modernDashboard && window.modernDashboard.showToast) {
            window.modernDashboard.showToast(message, type);
        }
    }
}

// Initialize Advanced Export Service
window.advancedExport = new AdvancedExportService();

#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  Tool,
} from '@modelcontextprotocol/sdk/types.js';
import { chromium, Browser, Page } from 'playwright';
import { SelectorAnalyzer } from './modules/SelectorAnalyzer.js';
import { SmartLoader } from './modules/SmartLoader.js';
import { BiNDupIntelligence } from './modules/BiNDupIntelligence.js';
import { DebugLogger } from './modules/DebugLogger.js';
import {
  AnalyzeSelectorInput,
  AnalyzeSelectorOutput,
  SmartWaitInput,
  SmartWaitOutput,
  DebugFlowInput,
  DebugFlowOutput,
  BiNDupOptimizeInput,
  BiNDupOptimizeOutput,
  MCPServerConfig
} from './types/index.js';

class PlaywrightMCPServer {
  private server: Server;
  private browser: Browser | null = null;
  private currentPage: Page | null = null;
  private selectorAnalyzer: SelectorAnalyzer;
  private smartLoader: SmartLoader;
  private bindupIntelligence: BiNDupIntelligence;
  private debugLogger: DebugLogger;
  private config: MCPServerConfig;

  constructor() {
    this.server = new Server(
      {
        name: 'playwright-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    // Initialize modules
    this.selectorAnalyzer = new SelectorAnalyzer();
    this.smartLoader = new SmartLoader();
    this.bindupIntelligence = new BiNDupIntelligence();
    this.debugLogger = new DebugLogger();

    // Default configuration
    this.config = {
      port: 3000,
      logLevel: 'info',
      dataStorage: {
        path: './data',
        maxSize: 100 * 1024 * 1024, // 100MB
        retention: 7 // 7 days
      },
      playwright: {
        headless: false,
        timeout: 30000,
        viewport: { width: 1920, height: 1080 }
      },
      bindui: {
        baseUrl: 'https://edit3.bindcloud.jp',
        defaultTimeouts: {
          launch: 30000,
          navigation: 20000,
          element: 10000
        },
        knownSelectors: {
          popupClose: ['#button-1014', '#button-1031', 'button:has-text("閉じる")'],
          siteItems: ['.cs-item[draggable="true"]', '.cs-select.cs-click'],
          loadingMasks: ['.x-mask', '.loading-mask']
        }
      }
    };

    this.setupToolHandlers();
  }

  private setupToolHandlers(): void {
    // List available tools
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'playwright-analyze-selectors',
            description: 'Analyze page selectors for stability and suggest robust alternatives',
            inputSchema: {
              type: 'object',
              properties: {
                url: { type: 'string', description: 'Page URL to analyze (optional if page already loaded)' },
                targetElement: { type: 'string', description: 'Specific element to analyze (optional)' },
                analysisDepth: { 
                  type: 'string', 
                  enum: ['basic', 'deep', 'comprehensive'],
                  description: 'Depth of analysis to perform'
                },
                context: { type: 'string', description: 'Context information (optional)' }
              },
              required: ['analysisDepth']
            }
          },
          {
            name: 'playwright-smart-wait',
            description: 'Intelligent waiting with adaptive timing and fallback strategies',
            inputSchema: {
              type: 'object',
              properties: {
                operation: { type: 'string', description: 'Operation being performed' },
                context: { type: 'string', description: 'Context of the operation' },
                maxWait: { type: 'number', description: 'Maximum wait time in milliseconds' },
                strategy: { 
                  type: 'string', 
                  enum: ['adaptive', 'race', 'sequential', 'intelligent'],
                  description: 'Loading strategy to use'
                }
              },
              required: ['operation', 'context', 'strategy']
            }
          },
          {
            name: 'playwright-debug-flow',
            description: 'Comprehensive flow debugging with visual feedback and insights',
            inputSchema: {
              type: 'object',
              properties: {
                step: { type: 'string', description: 'Current test step being debugged' },
                captureLevel: { 
                  type: 'string', 
                  enum: ['basic', 'detailed', 'comprehensive'],
                  description: 'Level of detail to capture'
                },
                highlightElements: { 
                  type: 'array', 
                  items: { type: 'string' },
                  description: 'Elements to highlight for visual debugging'
                },
                includeScreenshot: { type: 'boolean', description: 'Whether to include screenshot' },
                includeNetworkActivity: { type: 'boolean', description: 'Whether to include network monitoring' }
              },
              required: ['step', 'captureLevel']
            }
          },
          {
            name: 'playwright-bindui-optimize',
            description: 'BiNDup-specific optimizations and intelligent automation',
            inputSchema: {
              type: 'object',
              properties: {
                operation: { 
                  type: 'string', 
                  enum: ['launch', 'navigate', 'edit', 'save', 'popup-handle'],
                  description: 'BiNDup operation to optimize'
                },
                optimizationLevel: { 
                  type: 'string', 
                  enum: ['conservative', 'balanced', 'aggressive'],
                  description: 'Level of optimization to apply'
                },
                context: { type: 'string', description: 'Additional context information' }
              },
              required: ['operation', 'optimizationLevel']
            }
          },
          {
            name: 'playwright-init-browser',
            description: 'Initialize Playwright browser for testing',
            inputSchema: {
              type: 'object',
              properties: {
                headless: { type: 'boolean', description: 'Run browser in headless mode' },
                url: { type: 'string', description: 'Initial URL to navigate to' }
              },
              required: []
            }
          },
          {
            name: 'playwright-close-browser',
            description: 'Close Playwright browser and cleanup resources',
            inputSchema: {
              type: 'object',
              properties: {},
              required: []
            }
          }
        ] as Tool[]
      };
    });

    // Handle tool calls
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'playwright-analyze-selectors':
            return await this.handleAnalyzeSelectors(args as unknown as AnalyzeSelectorInput);

          case 'playwright-smart-wait':
            return await this.handleSmartWait(args as unknown as SmartWaitInput);

          case 'playwright-debug-flow':
            return await this.handleDebugFlow(args as unknown as DebugFlowInput);

          case 'playwright-bindui-optimize':
            return await this.handleBiNDupOptimize(args as unknown as BiNDupOptimizeInput);

          case 'playwright-init-browser':
            return await this.handleInitBrowser(args as unknown as { headless?: boolean; url?: string });

          case 'playwright-close-browser':
            return await this.handleCloseBrowser();

          default:
            throw new Error(`Unknown tool: ${name}`);
        }
      } catch (error) {
        console.error(`Error handling tool ${name}:`, error);
        return {
          content: [
            {
              type: 'text',
              text: `Error: ${error instanceof Error ? error.message : 'Unknown error'}`
            }
          ]
        };
      }
    });
  }

  private async ensurePage(): Promise<Page> {
    if (!this.browser) {
      this.browser = await chromium.launch({ 
        headless: this.config.playwright.headless,
        timeout: this.config.playwright.timeout
      });
    }

    if (!this.currentPage) {
      this.currentPage = await this.browser.newPage({
        viewport: this.config.playwright.viewport
      });
      
      // Start network monitoring
      await this.debugLogger.startNetworkMonitoring(this.currentPage);
    }

    return this.currentPage;
  }

  private async handleAnalyzeSelectors(input: AnalyzeSelectorInput) {
    const page = await this.ensurePage();

    // Check if URL is provided in context parameter
    let targetUrl = input.url;
    if (!targetUrl && input.context && input.context.includes('url:')) {
      const urlMatch = input.context.match(/url:([^\s]+)/);
      if (urlMatch) {
        targetUrl = urlMatch[1];
        console.log(`MCP Server output: Extracted URL from context: ${targetUrl}`);
      }
    }

    // Default to BiNDup site theater for popup analysis if no specific URL provided
    targetUrl = targetUrl || 'https://edit3.bindcloud.jp/bindcld/siteTheater/';

    try {
      await page.goto(targetUrl, { waitUntil: 'networkidle', timeout: 30000 });
      await page.waitForTimeout(2000); // Allow page to fully load

      // Try to trigger popup appearance for analysis
      if (targetUrl.includes('siteTheater') || targetUrl.includes('bindcloud')) {
        console.log('MCP Server output: Attempting to trigger popup for analysis...');
        try {
          // Look for common popup trigger elements and actual popups
          const popupElements = [
            '#button-1014',
            '#button-1031',
            '#id-first-guide-ok',
            'button:has-text("閉じる")',
            'button:has-text("OK")',
            '.popup-trigger',
            '.guide-trigger',
            '.cs-button',
            '.x-btn'
          ];

          let foundElements = 0;
          for (const selector of popupElements) {
            try {
              const element = page.locator(selector);
              if (await element.isVisible({ timeout: 1000 })) {
                console.log(`MCP Server output: Found popup element: ${selector}`);
                foundElements++;
              }
            } catch (error) {
              // Continue checking other selectors
            }
          }

          console.log(`MCP Server output: Found ${foundElements} popup-related elements for analysis`);
        } catch (error) {
          console.log('MCP Server output: No popup elements found, analyzing current state');
        }
      }
    } catch (error) {
      console.log(`MCP Server output: Navigation failed: ${error instanceof Error ? error.message : String(error)}`);
      // Continue with analysis on current page
    }

    let analysis: any[] = [];
    let recommendations: any[] = [];

    if (input.targetElement) {
      // Analyze specific element
      recommendations = [await this.selectorAnalyzer.findBestSelector(page, input.targetElement)];
    } else {
      // Analyze all page selectors
      analysis = await this.selectorAnalyzer.analyzePageSelectors(page, input.analysisDepth);
      recommendations = analysis.slice(0, 10).map(a => ({
        selector: a.element,
        confidence: a.confidence,
        reasoning: `Stability: ${a.stability}, Frequency: ${a.frequency}`,
        fallbacks: a.alternatives.slice(0, 3),
        stability: a.stability === 'stable' ? 'high' as const :
                  a.stability === 'dynamic' ? 'medium' as const : 'low' as const
      }));
    }

    const output: AnalyzeSelectorOutput = {
      recommendations,
      analysis,
      summary: {
        totalElements: analysis.length,
        stableElements: analysis.filter(a => a.stability === 'stable').length,
        dynamicElements: analysis.filter(a => a.stability === 'dynamic').length,
        unreliableElements: analysis.filter(a => a.stability === 'unreliable').length
      }
    };

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(output, null, 2)
        }
      ]
    };
  }

  private async handleSmartWait(input: SmartWaitInput) {
    const page = await this.ensurePage();
    
    const result = await this.smartLoader.smartWait(
      page,
      input.operation,
      input.context,
      input.strategy,
      input.maxWait,
      input.customConditions
    );

    const timingData = this.smartLoader.getTimingRecommendations(input.operation);
    const recommendations = [];

    if (timingData) {
      recommendations.push(`Average duration: ${timingData.averageDuration}ms`);
      recommendations.push(`Success rate: ${(timingData.successRate * 100).toFixed(1)}%`);
      recommendations.push(`Recommended timeout: ${timingData.averageDuration * 2}ms`);
    }

    if (!result.success) {
      recommendations.push('Consider using race condition strategy');
      recommendations.push('Check for loading masks or overlays');
      recommendations.push('Verify element selectors are correct');
    }

    const output: SmartWaitOutput = {
      success: result.success,
      duration: result.duration,
      strategy: result.strategy,
      conditions: result.conditions,
      recommendations
    };

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(output, null, 2)
        }
      ]
    };
  }

  private async handleDebugFlow(input: DebugFlowInput) {
    const page = await this.ensurePage();
    
    const snapshot = await this.debugLogger.captureFlowSnapshot(
      page,
      input.step,
      input.captureLevel,
      input.highlightElements || [],
      input.includeScreenshot !== false
    );

    const insights = this.debugLogger.generateInsights(snapshot);
    const recommendations = this.debugLogger.generateRecommendations(snapshot);

    const flowDocumentation = `
# Debug Flow: ${input.step}

## Page Information
- URL: ${snapshot.url}
- Timestamp: ${snapshot.timestamp.toISOString()}
- Elements Found: ${snapshot.elements.length}
- Network Events: ${snapshot.networkActivity.length}
- Console Messages: ${snapshot.consoleMessages.length}

## Performance Metrics
- Load Time: ${snapshot.performanceMetrics.loadTime}ms
- DOM Content Loaded: ${snapshot.performanceMetrics.domContentLoaded}ms

## Key Insights
${insights.map(insight => `- ${insight}`).join('\n')}

## Recommendations
${recommendations.map(rec => `- ${rec}`).join('\n')}
    `.trim();

    const output: DebugFlowOutput = {
      snapshot,
      insights,
      recommendations,
      flowDocumentation
    };

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            ...output,
            snapshot: {
              ...output.snapshot,
              screenshot: output.snapshot.screenshot ? '[Screenshot captured]' : undefined,
              domState: output.snapshot.domState.substring(0, 500) + '...'
            }
          }, null, 2)
        }
      ]
    };
  }

  private async handleBiNDupOptimize(input: BiNDupOptimizeInput) {
    const page = await this.ensurePage();
    
    // Perform BiNDup-specific optimizations
    const optimizations: string[] = [];
    let estimatedImprovement = 0;
    const risks: string[] = [];

    switch (input.operation) {
      case 'launch':
        await this.bindupIntelligence.optimizeBiNDupNavigation(page, 'launch');
        optimizations.push('Applied race condition loading strategy');
        optimizations.push('Enabled popup auto-handling');
        estimatedImprovement = 60; // 60% improvement expected
        break;

      case 'navigate':
        const loadingState = await this.bindupIntelligence.detectBiNDupLoadingState(page);
        optimizations.push(`Detected loading state: ${loadingState}`);
        
        if (loadingState === 'loading') {
          optimizations.push('Waiting for loading to complete');
          await this.smartLoader.smartWait(page, 'bindui-loading', 'navigation', 'intelligent');
        }
        
        estimatedImprovement = 40;
        break;

      case 'popup-handle':
        const handled = await this.bindupIntelligence.handleBiNDupPopups(page);
        optimizations.push(`Handled popups: ${handled}`);
        estimatedImprovement = handled ? 80 : 10;
        break;

      default:
        optimizations.push('No specific optimizations available for this operation');
    }

    const recommendations = this.bindupIntelligence.getBiNDupRecommendations(
      input.operation, 
      input.context || ''
    );

    const output: BiNDupOptimizeOutput = {
      optimizations,
      estimatedImprovement,
      risks,
      recommendations
    };

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify(output, null, 2)
        }
      ]
    };
  }

  private async handleInitBrowser(args: { headless?: boolean; url?: string }) {
    if (this.browser) {
      await this.browser.close();
    }

    this.browser = await chromium.launch({ 
      headless: args.headless ?? this.config.playwright.headless 
    });
    
    this.currentPage = await this.browser.newPage({
      viewport: this.config.playwright.viewport
    });

    await this.debugLogger.startNetworkMonitoring(this.currentPage);

    if (args.url) {
      await this.currentPage.goto(args.url);
    }

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            success: true,
            message: 'Browser initialized successfully',
            headless: args.headless ?? this.config.playwright.headless,
            url: args.url || 'No initial URL'
          }, null, 2)
        }
      ]
    };
  }

  private async handleCloseBrowser() {
    if (this.browser) {
      await this.browser.close();
      this.browser = null;
      this.currentPage = null;
    }

    return {
      content: [
        {
          type: 'text',
          text: JSON.stringify({
            success: true,
            message: 'Browser closed successfully'
          }, null, 2)
        }
      ]
    };
  }

  async run(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Playwright MCP Server running on stdio');
  }
}

// Start the server
const server = new PlaywrightMCPServer();
server.run().catch(console.error);

# 🎯 BiNDup Test Automation - Successful Patterns Documentation

## 📋 **Overview**
This document captures the proven patterns and best practices discovered during the development of the Image Management Test Suite. These patterns ensure reliable, fast, and maintainable test automation for BiNDup.

---

## 🏆 **Core Success Patterns**

### **1. Iframe Handling Strategy**
```typescript
// ✅ PROVEN PATTERN: Dual iframe detection and handling
const blockEditorVisible = await editorPageHandle.locator('iframe[name="blockeditor"]').isVisible({ timeout: 5000 });
const blockTemplateVisible = await editorPageHandle.locator('iframe[name="blockTemplate"]').isVisible({ timeout: 5000 });

if (blockTemplateVisible) {
  // Handle template selection workflow
  const templateFrame = editorPageHandle.locator('iframe[name="blockTemplate"]').contentFrame();
} else if (blockEditorVisible) {
  // Handle direct block editing workflow
  const editorFrame = editorPageHandle.locator('iframe[name="blockeditor"]').contentFrame();
}
```

### **2. Smart Element Detection**
```typescript
// ✅ PROVEN PATTERN: Multiple selector fallbacks with graceful degradation
const imageSelectors = [
  'img',                           // Direct images
  '[role="img"]',                  // Accessible images
  '.image-element',                // Class-based
  '[data-type="image"]',           // Data attribute
  'div[style*="background-image"]' // Background images
];

for (const selector of imageSelectors) {
  try {
    const elements = frame.locator(selector);
    const count = await elements.count();
    if (count > 0) {
      await elements.first().click();
      success = true;
      break;
    }
  } catch (error) { continue; }
}
```

### **3. Comprehensive Popup Management**
```typescript
// ✅ PROVEN PATTERN: Proactive popup detection and handling
const popupSelectors = [
  '#button-1014',                  // Start guide popup
  '#button-1031',                  // Secondary start guide
  '#id-first-guide-ok',            // Image editor guide
  'button:has-text("閉じる")',      // Generic close buttons
  '.x-tool-close'                  // Modal close buttons
];

for (const selector of popupSelectors) {
  try {
    const popup = page.locator(selector);
    if (await popup.isVisible({ timeout: 2000 })) {
      await popup.click();
      await page.waitForTimeout(1000);
    }
  } catch (error) { continue; }
}
```

---

## ⚡ **Performance Optimization Patterns**

### **1. Smart Timeout Configuration**
```typescript
// ✅ PROVEN PATTERN: Fast detection, adequate operation time
const CONFIG = {
  ELEMENT_TIMEOUT: 10000,          // 10s for element detection
  POPUP_DETECTION_TIMEOUT: 2000,  // 2s for popup detection (fast)
  STEP_WAIT: 2000,                 // 2s between steps
  EDITOR_LOAD_WAIT: 5000,          // 5s for editor loading
  BULK_OPERATION_TIMEOUT: 300000   // 5min for bulk operations
};
```

### **2. Efficient Retry Mechanisms**
```typescript
// ✅ PROVEN PATTERN: Limited retries with exponential backoff
async function executeWithRetry(operation, maxAttempts = 3) {
  for (let attempt = 1; attempt <= maxAttempts; attempt++) {
    try {
      await operation();
      return; // Success
    } catch (error) {
      if (attempt === maxAttempts) throw error;
      await page.waitForTimeout(1000 * attempt); // Exponential backoff
    }
  }
}
```

---

## 🎯 **Site Editor Integration Patterns**

### **1. Proven Site Editor Setup Flow**
```typescript
// ✅ PROVEN PATTERN: Reusable site editor setup (9 steps)
async function executeSiteEditorSetupSteps() {
  // Steps 1-4: Authentication and BiNDup launch
  await authenticateAndLaunchBiNDup();
  
  // Steps 5-6: Navigation and popup handling
  await navigateToSiteTheaterAndSelectSite();
  
  // Steps 7-9: Site editor preparation
  await enterSiteEditingMode();
}
```

### **2. Block Interaction Pattern**
```typescript
// ✅ PROVEN PATTERN: Block selection and toolbar activation
async function interactWithBlock() {
  // 1. Click block to select
  const block = previewFrame.locator('.b-plain.cssskin-_block_billboard').first();
  await block.click({ force: true });
  await page.waitForTimeout(2000);
  
  // 2. Verify toolbar appeared
  const editButton = previewFrame.locator('#block_edit span');
  const toolbarVisible = await editButton.isVisible({ timeout: 5000 });
  
  // 3. Click edit button
  if (toolbarVisible) {
    await editButton.click();
  }
}
```

---

## 🖼️ **Image Management Patterns**

### **1. Image Upload with Verification**
```typescript
// ✅ PROVEN PATTERN: Upload with comprehensive monitoring
async function uploadImageWithVerification(imagePath) {
  // Get initial count
  const initialCount = await getImageCount();
  
  // Upload via file chooser
  const fileChooserPromise = page.waitForEvent('filechooser');
  await page.locator('[data-tooltip="画像取込み"]').click();
  const fileChooser = await fileChooserPromise;
  await fileChooser.setFiles(imagePath);
  
  // Monitor upload completion
  await page.waitForResponse(response => 
    response.url().includes('uploadImgFile') && response.status() === 200
  );
  
  // Verify count increased
  const finalCount = await getImageCount();
  assert(finalCount > initialCount, 'Image upload failed');
}
```

### **2. Image Verification in Blocks**
```typescript
// ✅ PROVEN PATTERN: Actual image verification (not just template selection)
async function verifyImageInBlock() {
  const previewFrame = page.locator('iframe[name="preview"]').contentFrame();
  const blockImages = previewFrame.locator('.b-plain img, .b-plain [style*="background-image"]');
  const imageCount = await blockImages.count();
  
  if (imageCount > 0) {
    // Get actual image details
    for (let i = 0; i < imageCount; i++) {
      const img = blockImages.nth(i);
      const src = await img.getAttribute('src');
      const alt = await img.getAttribute('alt');
      console.log(`✅ Image ${i + 1}: src="${src}", alt="${alt}"`);
    }
    return true;
  }
  return false;
}
```

---

## 🔧 **Error Handling Patterns**

### **1. Graceful Degradation**
```typescript
// ✅ PROVEN PATTERN: Continue on non-critical failures
async function performOptionalOperation() {
  try {
    await criticalOperation();
  } catch (error) {
    console.log(`Optional operation failed: ${error.message}`);
    // Continue execution - don't fail the entire test
  }
}
```

### **2. Context-Aware Error Recovery**
```typescript
// ✅ PROVEN PATTERN: Smart error recovery based on context
async function recoverFromError(error, context) {
  if (context === 'popup_detection') {
    // Popup not found is often normal
    console.log('No popup found, continuing...');
    return;
  } else if (context === 'image_upload') {
    // Image upload failure is critical
    throw new Error(`Critical: Image upload failed - ${error.message}`);
  }
}
```

---

## 📊 **Test Structure Patterns**

### **1. Modular Test Design**
```typescript
// ✅ PROVEN PATTERN: Reusable operation functions
async function executeImageOperation(operation, operationName) {
  return await PerformanceMonitor.monitorOperation(
    operation,
    operationName,
    CONFIG.OPERATION_TIMEOUT
  );
}

// Usage in tests
await executeImageOperation(
  async () => {
    await uploadImage();
    await verifyUpload();
  },
  'Upload and Verify Image'
);
```

### **2. Performance Monitoring Integration**
```typescript
// ✅ PROVEN PATTERN: Built-in performance tracking
await PerformanceMonitor.monitorOperation(
  async () => {
    // Test operations
  },
  'Operation Name',
  timeoutMs
);
```

---

## 🎯 **Selector Strategy Patterns**

### **1. Robust Selector Hierarchy**
```typescript
// ✅ PROVEN PATTERN: Fallback selector strategy
const selectorHierarchy = [
  '#specific-id',                    // Most specific
  '.specific-class',                 // Class-based
  '[data-testid="element"]',         // Data attributes
  'button:has-text("Button Text")',  // Text-based
  'button'                           // Generic fallback
];
```

### **2. Dynamic ID Avoidance**
```typescript
// ❌ AVOID: Dynamic IDs that change between runs
// await page.locator('#button-1083').click();

// ✅ USE: Stable selectors
await page.locator('button:has-text("OK")').click();
await page.locator('[data-tooltip="画像取込み"]').click();
await page.locator('.x-btn:has-text("適用")').click();
```

---

## 🚀 **CI/CD Integration Patterns**

### **1. Test Categorization**
```typescript
// ✅ PROVEN PATTERN: Clear test categorization
test.describe('🖼️ Image Management - Upload and Operations', () => {
  test('IMT-01: Complete Image Management Flow', async () => {
    // Core functionality test
  });
  
  test('IMT-02: Add Image to Block Flow', async () => {
    // Integration test
  });
});
```

### **2. Environment Configuration**
```typescript
// ✅ PROVEN PATTERN: Environment-aware configuration
const CONFIG = {
  BASE_URL: process.env.TEST_BASE_URL || 'https://mypage.weblife.me',
  TIMEOUT_MULTIPLIER: process.env.CI ? 2 : 1, // Longer timeouts in CI
  HEADLESS: process.env.CI ? true : false,     // Headless in CI
};
```

---

## 📈 **Success Metrics**

### **Achieved Performance Benchmarks**
- ✅ **Test Execution**: 4 tests in ~8.5 minutes
- ✅ **Success Rate**: 100% (4/4 tests passing)
- ✅ **Reliability**: Consistent results across multiple runs
- ✅ **Maintainability**: Modular, reusable components

### **Key Success Indicators**
- ✅ **Actual Image Verification**: `src="_src/90244340/test.png?v=1752629257682"`
- ✅ **Comprehensive Coverage**: Upload, edit, delete, block integration
- ✅ **Robust Error Handling**: Graceful degradation and recovery
- ✅ **Performance Optimized**: Smart timeouts and efficient operations

---

## 🎉 **Conclusion**

These patterns represent battle-tested approaches for BiNDup test automation. They provide:

1. **Reliability**: Consistent test execution across different environments
2. **Maintainability**: Modular, reusable components
3. **Performance**: Optimized execution times
4. **Robustness**: Comprehensive error handling and recovery

**Use these patterns as the foundation for all future BiNDup test development!** 🚀

import { Page } from 'playwright';
import { 
  DebugSnapshot, 
  ElementInfo, 
  NetworkEvent, 
  ConsoleMessage, 
  PerformanceMetrics 
} from '../types/index.js';

export class DebugLogger {
  private networkEvents: NetworkEvent[] = [];
  private consoleMessages: ConsoleMessage[] = [];
  private snapshots: DebugSnapshot[] = [];
  private maxHistorySize = 100;

  constructor() {
  }

  /**
   * Capture comprehensive flow snapshot
   */
  async captureFlowSnapshot(
    page: Page, 
    step: string, 
    captureLevel: 'basic' | 'detailed' | 'comprehensive' = 'detailed',
    highlightElements: string[] = [],
    includeScreenshot: boolean = true
  ): Promise<DebugSnapshot> {
    try {
      console.log(`Capturing ${captureLevel} snapshot for step: ${step}`);

      // Highlight elements if requested
      if (highlightElements.length > 0) {
        await this.highlightElements(page, highlightElements);
      }

      const snapshot: DebugSnapshot = {
        timestamp: new Date(),
        step,
        url: page.url(),
        elements: await this.captureElementInfo(page, captureLevel),
        networkActivity: [...this.networkEvents],
        consoleMessages: [...this.consoleMessages],
        screenshot: includeScreenshot ? await this.captureScreenshot(page) : undefined,
        domState: await this.captureDOMState(page, captureLevel),
        performanceMetrics: await this.capturePerformanceMetrics(page)
      };

      // Store snapshot
      this.snapshots.push(snapshot);
      if (this.snapshots.length > this.maxHistorySize) {
        this.snapshots = this.snapshots.slice(-this.maxHistorySize);
      }

      // Clear temporary data
      this.networkEvents = [];
      this.consoleMessages = [];

      console.log(`Snapshot captured: ${snapshot.elements.length} elements, ${snapshot.networkActivity.length} network events`);
      return snapshot;

    } catch (error) {
      console.error('Failed to capture flow snapshot:', error);
      throw error;
    }
  }

  /**
   * Highlight elements on the page for visual debugging
   */
  async highlightElements(page: Page, selectors: string[]): Promise<void> {
    try {
      // Add highlight styles to the page
      await page.addStyleTag({
        content: `
          .playwright-debug-highlight {
            outline: 3px solid #ff0000 !important;
            outline-offset: 2px !important;
            background-color: rgba(255, 0, 0, 0.1) !important;
            position: relative !important;
            z-index: 9999 !important;
          }
          .playwright-debug-highlight::before {
            content: "🎯 DEBUG" !important;
            position: absolute !important;
            top: -25px !important;
            left: 0 !important;
            background: #ff0000 !important;
            color: white !important;
            padding: 2px 6px !important;
            font-size: 12px !important;
            font-weight: bold !important;
            z-index: 10000 !important;
          }
        `
      });

      for (const selector of selectors) {
        try {
          await page.locator(selector).evaluateAll((elements) => {
            elements.forEach((el: Element) => {
              el.classList.add('playwright-debug-highlight');
            });
          });
        } catch (error) {
          console.log(`Could not highlight selector: ${selector}`);
        }
      }

      // Remove highlights after a delay
      setTimeout(async () => {
        try {
          await page.evaluate(() => {
            document.querySelectorAll('.playwright-debug-highlight').forEach(el => {
              el.classList.remove('playwright-debug-highlight');
            });
          });
        } catch (error) {
          // Ignore cleanup errors
        }
      }, 5000);

    } catch (error) {
      console.error('Failed to highlight elements:', error);
    }
  }

  /**
   * Start monitoring network activity
   */
  async startNetworkMonitoring(page: Page): Promise<void> {
    try {
      page.on('request', (request) => {
        this.networkEvents.push({
          url: request.url(),
          method: request.method(),
          status: 0, // Will be updated on response
          duration: 0, // Will be calculated on response
          timestamp: new Date(),
          type: 'request'
        });
      });

      page.on('response', (response) => {
        const requestEvent = this.networkEvents.find(
          event => event.url === response.url() && event.type === 'request' && event.status === 0
        );

        if (requestEvent) {
          requestEvent.status = response.status();
          requestEvent.duration = Date.now() - requestEvent.timestamp.getTime();
        }

        this.networkEvents.push({
          url: response.url(),
          method: response.request().method(),
          status: response.status(),
          duration: Date.now() - response.request().timing().requestStart,
          timestamp: new Date(),
          type: 'response'
        });
      });

      page.on('console', (message) => {
        this.consoleMessages.push({
          type: message.type() as any,
          text: message.text(),
          timestamp: new Date(),
          location: message.location() ? `${message.location().url}:${message.location().lineNumber}` : undefined
        });
      });

      console.log('Network monitoring started');

    } catch (error) {
      console.error('Failed to start network monitoring:', error);
    }
  }

  /**
   * Generate insights from captured data
   */
  generateInsights(snapshot: DebugSnapshot): string[] {
    const insights: string[] = [];

    // Performance insights
    if (snapshot.performanceMetrics.loadTime > 10000) {
      insights.push(`⚠️ Slow page load detected: ${snapshot.performanceMetrics.loadTime}ms`);
    }

    // Network insights
    const failedRequests = snapshot.networkActivity.filter(event => 
      event.type === 'response' && event.status >= 400
    );
    if (failedRequests.length > 0) {
      insights.push(`❌ ${failedRequests.length} failed network requests detected`);
    }

    const slowRequests = snapshot.networkActivity.filter(event => 
      event.duration > 5000
    );
    if (slowRequests.length > 0) {
      insights.push(`🐌 ${slowRequests.length} slow network requests (>5s) detected`);
    }

    // Element insights
    const hiddenElements = snapshot.elements.filter(el => !el.visible);
    if (hiddenElements.length > snapshot.elements.length * 0.5) {
      insights.push(`👻 Many elements are hidden (${hiddenElements.length}/${snapshot.elements.length})`);
    }

    const disabledElements = snapshot.elements.filter(el => !el.enabled);
    if (disabledElements.length > 0) {
      insights.push(`🚫 ${disabledElements.length} disabled elements found`);
    }

    // Console insights
    const errors = snapshot.consoleMessages.filter(msg => msg.type === 'error');
    if (errors.length > 0) {
      insights.push(`🔥 ${errors.length} console errors detected`);
    }

    const warnings = snapshot.consoleMessages.filter(msg => msg.type === 'warn');
    if (warnings.length > 0) {
      insights.push(`⚠️ ${warnings.length} console warnings detected`);
    }

    // BiNDup-specific insights
    if (snapshot.url.includes('bindcloud')) {
      const loadingMasks = snapshot.elements.filter(el => 
        el.selector.includes('x-mask') || el.selector.includes('loading')
      );
      if (loadingMasks.length > 0) {
        insights.push(`⏳ BiNDup loading masks detected: ${loadingMasks.length}`);
      }

      const popups = snapshot.elements.filter(el => 
        el.textContent?.includes('閉じる') || el.selector.includes('button-')
      );
      if (popups.length > 0) {
        insights.push(`🔔 Potential BiNDup popups detected: ${popups.length}`);
      }
    }

    return insights;
  }

  /**
   * Generate recommendations based on snapshot
   */
  generateRecommendations(snapshot: DebugSnapshot): string[] {
    const recommendations: string[] = [];

    // Performance recommendations
    if (snapshot.performanceMetrics.loadTime > 10000) {
      recommendations.push('Consider using race conditions for loading states');
      recommendations.push('Implement timeout optimizations');
    }

    // Network recommendations
    const failedRequests = snapshot.networkActivity.filter(event => 
      event.type === 'response' && event.status >= 400
    );
    if (failedRequests.length > 0) {
      recommendations.push('Implement retry mechanisms for failed requests');
      recommendations.push('Add error handling for network failures');
    }

    // Element recommendations
    const hiddenElements = snapshot.elements.filter(el => !el.visible);
    if (hiddenElements.length > 0) {
      recommendations.push('Use waitForSelector with visible state');
      recommendations.push('Consider force clicks for hidden elements');
    }

    // BiNDup-specific recommendations
    if (snapshot.url.includes('bindcloud')) {
      recommendations.push('Use BiNDup-specific popup handling');
      recommendations.push('Implement loading mask detection');
      recommendations.push('Use stable selectors for BiNDup elements');
    }

    return recommendations;
  }

  /**
   * Capture element information
   */
  private async captureElementInfo(page: Page, level: 'basic' | 'detailed' | 'comprehensive'): Promise<ElementInfo[]> {
    let selector = '';
    
    switch (level) {
      case 'basic':
        selector = 'button, a, input, [role="button"]';
        break;
      case 'detailed':
        selector = 'button, a, input, select, div[onclick], [role="button"], [data-action]';
        break;
      case 'comprehensive':
        selector = '*[onclick], *[role], button, a, input, select, div, span';
        break;
    }

    try {
      const elements = await page.locator(selector).evaluateAll((elements) => {
        return elements.map((el, index) => {
          const rect = el.getBoundingClientRect();
          const computedStyle = window.getComputedStyle(el);

          // Generate a simple selector for the element
          let elementSelector = el.tagName.toLowerCase();
          if (el.id) {
            elementSelector = `#${el.id}`;
          } else if (el.className) {
            const classes = el.className.split(' ').filter((c: string) => c.trim());
            if (classes.length > 0) {
              elementSelector = `.${classes[0]}`;
            }
          }

          return {
            selector: elementSelector,
            tagName: el.tagName.toLowerCase(),
            visible: computedStyle.display !== 'none' &&
                    computedStyle.visibility !== 'hidden' &&
                    rect.width > 0 && rect.height > 0,
            enabled: !(el as any).disabled &&
                    computedStyle.pointerEvents !== 'none',
            textContent: el.textContent?.trim() || undefined,
            attributes: Object.fromEntries(
              Array.from(el.attributes).map(attr => [attr.name, attr.value])
            ),
            boundingBox: rect.width > 0 && rect.height > 0 ? {
              x: rect.x,
              y: rect.y,
              width: rect.width,
              height: rect.height
            } : undefined
          };
        });
      });

      return elements;

    } catch (error) {
      console.error('Failed to capture element info:', error);
      return [];
    }
  }

  /**
   * Capture screenshot
   */
  private async captureScreenshot(page: Page): Promise<Buffer | undefined> {
    try {
      return await page.screenshot({ 
        fullPage: true,
        type: 'png'
      });
    } catch (error) {
      console.error('Failed to capture screenshot:', error);
      return undefined;
    }
  }

  /**
   * Capture DOM state
   */
  private async captureDOMState(page: Page, level: 'basic' | 'detailed' | 'comprehensive'): Promise<string> {
    try {
      if (level === 'basic') {
        return await page.evaluate(() => document.title);
      } else if (level === 'detailed') {
        return await page.evaluate(() => {
          const head = document.head.innerHTML.substring(0, 1000);
          const body = document.body.innerHTML.substring(0, 2000);
          return `HEAD: ${head}\nBODY: ${body}`;
        });
      } else {
        return await page.content();
      }
    } catch (error) {
      console.error('Failed to capture DOM state:', error);
      return 'Failed to capture DOM state';
    }
  }

  /**
   * Capture performance metrics
   */
  private async capturePerformanceMetrics(page: Page): Promise<PerformanceMetrics> {
    try {
      const metrics = await page.evaluate(() => {
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
        const paint = performance.getEntriesByType('paint');
        
        return {
          loadTime: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
          domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
          firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime,
          largestContentfulPaint: paint.find(p => p.name === 'largest-contentful-paint')?.startTime,
          memoryUsage: (performance as any).memory ? (performance as any).memory.usedJSHeapSize : undefined
        };
      });

      return metrics;

    } catch (error) {
      console.error('Failed to capture performance metrics:', error);
      return {
        loadTime: 0,
        domContentLoaded: 0
      };
    }
  }

  /**
   * Get all snapshots
   */
  getSnapshots(): DebugSnapshot[] {
    return [...this.snapshots];
  }

  /**
   * Get latest snapshot
   */
  getLatestSnapshot(): DebugSnapshot | null {
    return this.snapshots.length > 0 ? this.snapshots[this.snapshots.length - 1] : null;
  }

  /**
   * Clear history
   */
  clearHistory(): void {
    this.snapshots = [];
    this.networkEvents = [];
    this.consoleMessages = [];
  }
}

/**
 * 🤖 Gemini AI Integration Service
 * Advanced AI analysis for test results using Google Gemini API
 */

class GeminiAIService {
    constructor() {
        this.apiKey = null;
        this.isEnabled = localStorage.getItem('gemini-ai-enabled') === 'true';
        this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent';
        this.chatModel = 'gemini-pro';
        this.analysisCache = new Map();
        
        this.init();
    }

    init() {
        // Load API key from localStorage if available
        this.apiKey = localStorage.getItem('gemini-api-key');
        
        // Set up UI elements
        this.setupAIToggle();
        this.setupAPIKeyInput();
        this.setupChatInterface();
    }

    setupAIToggle() {
        const toggleContainer = document.createElement('div');
        toggleContainer.className = 'fixed top-20 right-4 z-50';
        toggleContainer.innerHTML = `
            <div class="glass-morphism rounded-2xl p-4 border border-white/20">
                <div class="flex items-center space-x-3">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-brain text-purple-400"></i>
                        <span class="text-sm font-medium text-gray-700 dark:text-gray-300">AI Analysis</span>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" id="ai-toggle" class="sr-only peer" ${this.isEnabled ? 'checked' : ''}>
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 dark:peer-focus:ring-purple-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-purple-600"></div>
                    </label>
                </div>
                <div id="ai-status" class="mt-2 text-xs ${this.isEnabled ? 'text-green-600' : 'text-gray-500'}">
                    ${this.isEnabled ? '🟢 AI Analysis Enabled' : '⚪ AI Analysis Disabled'}
                </div>
            </div>
        `;
        
        document.body.appendChild(toggleContainer);
        
        // Add event listener
        const toggle = document.getElementById('ai-toggle');
        toggle.addEventListener('change', (e) => {
            this.toggleAI(e.target.checked);
        });
    }

    setupAPIKeyInput() {
        const keyInputContainer = document.createElement('div');
        keyInputContainer.id = 'api-key-modal';
        keyInputContainer.className = 'fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden flex items-center justify-center';
        keyInputContainer.innerHTML = `
            <div class="glass-morphism rounded-3xl p-8 max-w-md w-full mx-4 border border-white/20">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-purple-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="fas fa-key text-purple-400 text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">Gemini AI Configuration</h3>
                    <p class="text-gray-600 dark:text-gray-400 text-sm">Enter your Google Gemini API key to enable AI analysis</p>
                </div>
                
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">API Key</label>
                        <input type="password" id="gemini-api-key" placeholder="Enter your Gemini API key" 
                               class="w-full px-4 py-3 bg-white/10 border border-white/20 rounded-xl text-gray-900 dark:text-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-purple-500">
                    </div>
                    
                    <div class="flex space-x-3">
                        <button id="save-api-key" class="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-4 py-3 rounded-xl font-medium transition-colors">
                            <i class="fas fa-save mr-2"></i>Save & Enable
                        </button>
                        <button id="cancel-api-key" class="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-4 py-3 rounded-xl font-medium transition-colors">
                            Cancel
                        </button>
                    </div>
                    
                    <div class="text-xs text-gray-500 dark:text-gray-400 text-center">
                        <a href="https://makersuite.google.com/app/apikey" target="_blank" class="text-purple-400 hover:text-purple-300">
                            Get your free Gemini API key →
                        </a>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(keyInputContainer);
        
        // Add event listeners
        document.getElementById('save-api-key').addEventListener('click', () => this.saveAPIKey());
        document.getElementById('cancel-api-key').addEventListener('click', () => this.hideAPIKeyModal());
    }

    setupChatInterface() {
        const chatContainer = document.createElement('div');
        chatContainer.id = 'ai-chat-modal';
        chatContainer.className = 'fixed inset-0 bg-gradient-to-br from-black/60 via-purple-900/20 to-black/60 backdrop-blur-lg z-50 hidden';
        chatContainer.innerHTML = `
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="bg-white/95 dark:bg-gray-900/95 backdrop-blur-xl rounded-3xl w-full max-w-5xl max-h-[90vh] overflow-hidden shadow-2xl border-2 border-purple-200 dark:border-purple-800">
                    <!-- Chat Header -->
                    <div class="bg-gradient-to-r from-purple-600 to-blue-600 p-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <div class="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center backdrop-blur-sm">
                                    <i class="fas fa-robot text-white text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="text-xl font-bold text-white">🤖 Gemini AI Assistant</h3>
                                    <p class="text-purple-100 text-sm">Intelligent test analysis and insights</p>
                                </div>
                            </div>
                            <button id="close-ai-chat" class="w-10 h-10 bg-white/20 rounded-xl flex items-center justify-center hover:bg-white/30 transition-all duration-200 backdrop-blur-sm">
                                <i class="fas fa-times text-white"></i>
                            </button>
                        </div>

                        <!-- AI Status Bar -->
                        <div class="mt-4 flex items-center space-x-3 text-sm text-purple-100">
                            <div class="flex items-center space-x-2">
                                <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                                <span>AI Online</span>
                            </div>
                            <div class="w-px h-4 bg-white/20"></div>
                            <span>Analyzing your test data</span>
                            <div class="w-px h-4 bg-white/20"></div>
                            <span id="ai-response-time">Response time: ~2s</span>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div id="ai-chat-messages" class="h-96 overflow-y-auto p-6 space-y-6 bg-gray-50 dark:bg-gray-800">
                        <div class="flex items-start space-x-4">
                            <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg">
                                <i class="fas fa-robot text-white text-sm"></i>
                            </div>
                            <div class="bg-white dark:bg-gray-700 rounded-2xl p-4 max-w-2xl shadow-lg border border-gray-200 dark:border-gray-600">
                                <div class="flex items-center space-x-2 mb-2">
                                    <span class="text-sm font-semibold text-purple-600 dark:text-purple-400">Gemini AI</span>
                                    <span class="text-xs text-gray-500 dark:text-gray-400">Just now</span>
                                </div>
                                <p class="text-gray-800 dark:text-gray-200 text-sm leading-relaxed">
                                    👋 Hello! I'm your intelligent test analysis assistant. I can help you:
                                    <br><br>
                                    🔍 <strong>Analyze test failures</strong> and identify patterns<br>
                                    📊 <strong>Review performance metrics</strong> and bottlenecks<br>
                                    💡 <strong>Suggest improvements</strong> for your test suite<br>
                                    🎯 <strong>Predict future issues</strong> based on trends<br>
                                    <br>
                                    What would you like to explore about your test results?
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="px-6 py-3 bg-gray-100 dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
                        <div class="flex flex-wrap gap-2">
                            <button class="ai-quick-question bg-gradient-to-r from-red-500 to-red-600 text-white px-4 py-2 rounded-xl text-xs font-medium hover:from-red-600 hover:to-red-700 transition-all duration-200 shadow-md" data-question="Analyze my test failures and identify the root causes">
                                🔍 Analyze Failures
                            </button>
                            <button class="ai-quick-question bg-gradient-to-r from-amber-500 to-orange-500 text-white px-4 py-2 rounded-xl text-xs font-medium hover:from-amber-600 hover:to-orange-600 transition-all duration-200 shadow-md" data-question="What are the performance bottlenecks in my test suite?">
                                ⚡ Performance Issues
                            </button>
                            <button class="ai-quick-question bg-gradient-to-r from-green-500 to-green-600 text-white px-4 py-2 rounded-xl text-xs font-medium hover:from-green-600 hover:to-green-700 transition-all duration-200 shadow-md" data-question="Suggest specific improvements for my test automation">
                                💡 Improvements
                            </button>
                            <button class="ai-quick-question bg-gradient-to-r from-blue-500 to-blue-600 text-white px-4 py-2 rounded-xl text-xs font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-200 shadow-md" data-question="Predict potential issues based on current test trends">
                                🔮 Predictions
                            </button>
                        </div>
                    </div>

                    <!-- Chat Input -->
                    <div class="p-6 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
                        <div class="flex space-x-4">
                            <div class="flex-1 relative">
                                <input type="text" id="ai-chat-input" placeholder="Ask me anything about your test results..."
                                       class="w-full px-4 py-4 bg-gray-100 dark:bg-gray-800 border-2 border-gray-200 dark:border-gray-700 rounded-2xl text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent transition-all duration-200">
                                <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                    <kbd class="px-2 py-1 text-xs font-semibold text-gray-500 bg-gray-200 dark:bg-gray-700 dark:text-gray-400 rounded">Enter</kbd>
                                </div>
                            </div>
                            <button id="send-ai-message" class="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-6 py-4 rounded-2xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:scale-105">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>

                        <!-- Typing Indicator -->
                        <div id="ai-typing-indicator" class="hidden mt-4 flex items-center space-x-2 text-sm text-purple-600 dark:text-purple-400">
                            <div class="flex space-x-1">
                                <div class="w-2 h-2 bg-purple-500 rounded-full animate-bounce"></div>
                                <div class="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                                <div class="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                            </div>
                            <span class="font-medium">AI is analyzing your data...</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(chatContainer);
        
        // Add event listeners
        document.getElementById('close-ai-chat').addEventListener('click', () => this.hideChatModal());
        document.getElementById('send-ai-message').addEventListener('click', () => this.sendMessage());
        document.getElementById('ai-chat-input').addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });
        
        // Quick question buttons
        document.querySelectorAll('.ai-quick-question').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const question = e.target.dataset.question;
                document.getElementById('ai-chat-input').value = question;
                this.sendMessage();
            });
        });
    }

    toggleAI(enabled) {
        console.log('🤖 AI Toggle clicked:', enabled);
        this.isEnabled = enabled;
        localStorage.setItem('gemini-ai-enabled', enabled.toString());

        const statusEl = document.getElementById('ai-status');
        if (enabled) {
            console.log('🔑 Checking API key:', this.apiKey ? 'Present' : 'Missing');
            if (!this.apiKey) {
                console.log('🔑 No API key found, showing modal');
                this.showAPIKeyModal();
                return;
            }
            statusEl.textContent = '🟢 AI Analysis Enabled';
            statusEl.className = 'mt-2 text-xs text-green-600';
            console.log('🧠 Generating AI insights...');
            this.generateAIInsights();
        } else {
            statusEl.textContent = '⚪ AI Analysis Disabled';
            statusEl.className = 'mt-2 text-xs text-gray-500';
        }
    }

    showAPIKeyModal() {
        document.getElementById('api-key-modal').classList.remove('hidden');
    }

    hideAPIKeyModal() {
        document.getElementById('api-key-modal').classList.add('hidden');
        // Reset toggle if no API key
        if (!this.apiKey) {
            document.getElementById('ai-toggle').checked = false;
            this.isEnabled = false;
        }
    }

    saveAPIKey() {
        const apiKey = document.getElementById('gemini-api-key').value.trim();
        console.log('🔑 Saving API key:', apiKey ? 'Key provided' : 'No key');

        if (!apiKey) {
            this.showToast('Please enter a valid API key', 'error');
            return;
        }

        this.apiKey = apiKey;
        localStorage.setItem('gemini-api-key', apiKey);
        this.hideAPIKeyModal();

        // Enable AI and generate insights
        this.isEnabled = true;
        localStorage.setItem('gemini-ai-enabled', 'true');

        // Update toggle state
        const toggle = document.getElementById('ai-toggle');
        if (toggle) {
            toggle.checked = true;
        }

        document.getElementById('ai-status').textContent = '🟢 AI Analysis Enabled';
        document.getElementById('ai-status').className = 'mt-2 text-xs text-green-600';

        console.log('✅ API key saved, AI enabled');
        this.showToast('Gemini AI enabled successfully!', 'success');
        this.generateAIInsights();
    }

    showChatModal() {
        if (!this.isEnabled || !this.apiKey) {
            this.showToast('Please enable AI analysis first', 'warning');
            return;
        }
        document.getElementById('ai-chat-modal').classList.remove('hidden');
    }

    hideChatModal() {
        document.getElementById('ai-chat-modal').classList.add('hidden');
    }

    async sendMessage() {
        const input = document.getElementById('ai-chat-input');
        const message = input.value.trim();
        if (!message) return;
        
        // Add user message to chat
        this.addMessageToChat(message, 'user');
        input.value = '';
        
        // Show typing indicator
        document.getElementById('ai-typing-indicator').classList.remove('hidden');
        
        try {
            const response = await this.queryGemini(message);
            this.addMessageToChat(response, 'ai');
        } catch (error) {
            console.error('Gemini API error:', error);

            let errorMessage = 'Sorry, I encountered an error. ';

            if (error.message.includes('400')) {
                errorMessage += 'Please check your API key is valid and has the correct permissions. You can get a free API key from Google AI Studio.';
            } else if (error.message.includes('403')) {
                errorMessage += 'API key access denied. Please verify your API key has Gemini API access enabled.';
            } else if (error.message.includes('429')) {
                errorMessage += 'Rate limit exceeded. Please wait a moment and try again.';
            } else if (error.message.includes('No API key')) {
                errorMessage += 'Please configure your Gemini API key first.';
            } else {
                errorMessage += 'Please try again or check your internet connection.';
            }

            this.addMessageToChat(errorMessage, 'ai');
            this.showToast('AI request failed. Check console for details.', 'error');
        }
        
        // Hide typing indicator
        document.getElementById('ai-typing-indicator').classList.add('hidden');
    }

    addMessageToChat(message, sender) {
        const messagesContainer = document.getElementById('ai-chat-messages');
        const messageDiv = document.createElement('div');
        const timestamp = new Date().toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });

        if (sender === 'user') {
            messageDiv.className = 'flex items-start space-x-4 justify-end';
            messageDiv.innerHTML = `
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl p-4 max-w-2xl shadow-lg">
                    <div class="flex items-center space-x-2 mb-2">
                        <span class="text-sm font-semibold text-white">You</span>
                        <span class="text-xs text-blue-100">${timestamp}</span>
                    </div>
                    <p class="text-white text-sm leading-relaxed">${message}</p>
                </div>
                <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg">
                    <i class="fas fa-user text-white text-sm"></i>
                </div>
            `;
        } else {
            messageDiv.className = 'flex items-start space-x-4';

            // Format AI message with better styling
            const formattedMessage = this.formatAIMessage(message);

            messageDiv.innerHTML = `
                <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl flex items-center justify-center flex-shrink-0 shadow-lg">
                    <i class="fas fa-robot text-white text-sm"></i>
                </div>
                <div class="bg-white dark:bg-gray-700 rounded-2xl p-4 max-w-2xl shadow-lg border border-gray-200 dark:border-gray-600">
                    <div class="flex items-center space-x-2 mb-2">
                        <span class="text-sm font-semibold text-purple-600 dark:text-purple-400">Gemini AI</span>
                        <span class="text-xs text-gray-500 dark:text-gray-400">${timestamp}</span>
                        <div class="flex items-center space-x-1 ml-auto">
                            <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                            <span class="text-xs text-green-600 dark:text-green-400">Analyzed</span>
                        </div>
                    </div>
                    <div class="text-gray-800 dark:text-gray-200 text-sm leading-relaxed">${formattedMessage}</div>
                </div>
            `;
        }

        // Add smooth animation
        messageDiv.style.opacity = '0';
        messageDiv.style.transform = 'translateY(20px)';
        messagesContainer.appendChild(messageDiv);

        // Animate in
        setTimeout(() => {
            messageDiv.style.transition = 'all 0.3s ease-out';
            messageDiv.style.opacity = '1';
            messageDiv.style.transform = 'translateY(0)';
        }, 50);

        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    formatAIMessage(message) {
        // Enhanced formatting for AI messages
        let formatted = message
            // Bold text
            .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold text-purple-700 dark:text-purple-300">$1</strong>')
            // Bullet points
            .replace(/^- (.*$)/gim, '<div class="flex items-start space-x-2 my-1"><span class="text-purple-500 mt-1">•</span><span>$1</span></div>')
            // Numbers
            .replace(/(\d+\.?\d*%?)/g, '<span class="font-semibold text-blue-600 dark:text-blue-400">$1</span>')
            // Line breaks
            .replace(/\n/g, '<br>')
            // Code blocks
            .replace(/`([^`]+)`/g, '<code class="bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded text-sm font-mono">$1</code>');

        return formatted;
    }

    async queryGemini(prompt, testData = null) {
        if (!this.apiKey) {
            throw new Error('No API key configured');
        }

        // Prepare context with test data if available
        let contextualPrompt = prompt;
        if (testData || window.DASHBOARD_DATA) {
            const data = testData || window.DASHBOARD_DATA;
            contextualPrompt = `
You are a test automation expert analyzing BiNDup test results. Based on the following test data:

📊 SUMMARY:
- Total Tests: ${data.summary.totalTests}
- Pass Rate: ${data.summary.passRate}%
- Failed Tests: ${data.summary.failedTests}
- Quality Score: ${data.summary.qualityScore}
- Execution Time: ${Math.round(data.summary.executionTime / 60000)} minutes

🧪 TEST SUITES:
${data.suites.map(s => `- ${s.name}: ${s.metrics.passRate}% (${s.metrics.passedTests}/${s.metrics.totalTests})`).join('\n')}

USER QUESTION: ${prompt}

Please provide a helpful, specific analysis with actionable insights. Use bullet points and be concise.
`;
        }

        try {
            const response = await fetch(`${this.baseUrl}?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    contents: [{
                        parts: [{
                            text: contextualPrompt
                        }]
                    }],
                    generationConfig: {
                        temperature: 0.7,
                        topK: 40,
                        topP: 0.95,
                        maxOutputTokens: 2048,
                        stopSequences: [],
                    },
                    safetySettings: [
                        {
                            category: "HARM_CATEGORY_HARASSMENT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_HATE_SPEECH",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        },
                        {
                            category: "HARM_CATEGORY_DANGEROUS_CONTENT",
                            threshold: "BLOCK_MEDIUM_AND_ABOVE"
                        }
                    ]
                })
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Gemini API Error Response:', errorText);
                throw new Error(`Gemini API error: ${response.status} - ${errorText}`);
            }

            const result = await response.json();
            console.log('Gemini API Response:', result);

            if (!result.candidates || !result.candidates[0] || !result.candidates[0].content) {
                throw new Error('Invalid response format from Gemini API');
            }

            return result.candidates[0].content.parts[0].text;

        } catch (error) {
            console.error('Gemini API Error:', error);
            throw error;
        }
    }

    async generateAIInsights() {
        if (!this.isEnabled || !this.apiKey || !window.dashboard?.data) return;
        
        try {
            const testData = window.dashboard.data;
            const prompt = `
Analyze this test automation report and provide:
1. Key insights about test performance
2. Risk assessment and areas of concern
3. Specific recommendations for improvement
4. Predictions for future test runs

Test Data Summary:
- Total Tests: ${testData.summary.totalTests}
- Pass Rate: ${testData.summary.passRate}%
- Failed Tests: ${testData.summary.failedTests}
- Quality Score: ${testData.summary.qualityScore}
- Execution Time: ${Math.round(testData.summary.executionTime / 60000)} minutes

Test Suites Performance:
${testData.suites.map(suite => 
    `- ${suite.name}: ${suite.metrics.passRate}% (${suite.metrics.passedTests}/${suite.metrics.totalTests})`
).join('\n')}

Please provide actionable insights in a professional format.
`;
            
            const aiInsights = await this.queryGemini(prompt, testData);
            this.displayAIInsights(aiInsights);
            
        } catch (error) {
            console.error('Failed to generate AI insights:', error);
            this.showToast('Failed to generate AI insights. Please check your API key.', 'error');
        }
    }

    displayAIInsights(insights) {
        // Add AI insights to the dashboard
        const insightsSection = document.getElementById('modern-key-findings');
        if (insightsSection) {
            const aiInsightDiv = document.createElement('div');
            aiInsightDiv.className = 'mt-6 p-4 bg-purple-500/10 rounded-xl border border-purple-500/20';
            aiInsightDiv.innerHTML = `
                <div class="flex items-center space-x-2 mb-3">
                    <i class="fas fa-robot text-purple-400"></i>
                    <h5 class="font-semibold text-purple-600 dark:text-purple-400">AI Analysis</h5>
                </div>
                <div class="text-sm text-gray-700 dark:text-gray-300 whitespace-pre-line">${insights}</div>
            `;
            insightsSection.appendChild(aiInsightDiv);
        }
    }

    showToast(message, type = 'info') {
        // Use the existing toast system from the main dashboard or create our own
        if (window.modernDashboard && window.modernDashboard.showToast) {
            window.modernDashboard.showToast(message, type);
        } else {
            // Create our own toast system
            this.createToast(message, type);
        }
    }

    createToast(message, type) {
        // Create toast container if it doesn't exist
        let toastContainer = document.getElementById('ai-toast-container');
        if (!toastContainer) {
            toastContainer = document.createElement('div');
            toastContainer.id = 'ai-toast-container';
            toastContainer.className = 'fixed top-4 right-4 z-50 space-y-2';
            document.body.appendChild(toastContainer);
        }

        // Create toast element
        const toast = document.createElement('div');
        const bgColor = {
            'success': 'bg-green-500',
            'error': 'bg-red-500',
            'warning': 'bg-amber-500',
            'info': 'bg-blue-500'
        }[type] || 'bg-blue-500';

        const icon = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-circle',
            'warning': 'fas fa-exclamation-triangle',
            'info': 'fas fa-info-circle'
        }[type] || 'fas fa-info-circle';

        toast.className = `${bgColor} text-white px-6 py-4 rounded-lg shadow-lg flex items-center space-x-3 transform transition-all duration-300 translate-x-full opacity-0`;
        toast.innerHTML = `
            <i class="${icon}"></i>
            <span class="font-medium">${message}</span>
            <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.remove()">
                <i class="fas fa-times"></i>
            </button>
        `;

        toastContainer.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.remove('translate-x-full', 'opacity-0');
        }, 100);

        // Auto remove after 5 seconds
        setTimeout(() => {
            toast.classList.add('translate-x-full', 'opacity-0');
            setTimeout(() => {
                if (toast.parentElement) {
                    toast.remove();
                }
            }, 300);
        }, 5000);
    }
}

// Initialize Gemini AI Service
window.geminiAI = new GeminiAIService();

/**
 * 🎨 BiNDup Test Intelligence Dashboard - Custom Styles
 * Beautiful, modern CSS for stunning test reporting
 */

/* ===== GLOBAL STYLES ===== */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
    line-height: 1.6;
    color: #1a202c;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

/* ===== UTILITY CLASSES ===== */
.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    color: transparent;
}

.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-bg-alt {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-bg-success {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-bg-warning {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.gradient-bg-danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
    opacity: 0;
}

.animate-slide-in-left {
    animation: slideInLeft 0.6s ease-out forwards;
    opacity: 0;
}

.animate-slide-in-right {
    animation: slideInRight 0.6s ease-out forwards;
    opacity: 0;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* ===== COMPONENT STYLES ===== */

/* Loading Screen */
#loading-screen {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    z-index: 9999;
}

#loading-screen .animate-spin {
    border-top-color: transparent;
    border-right-color: transparent;
    border-bottom-color: white;
    border-left-color: white;
}

/* Header */
header.glass-effect {
    position: sticky;
    top: 0;
    z-index: 1000;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
}

/* Metric Cards */
.metric-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.metric-card:hover::before {
    left: 100%;
}

.metric-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Progress Rings */
.progress-ring {
    transform: rotate(-90deg);
}

.progress-ring-circle {
    transition: stroke-dashoffset 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: 50% 50%;
}

/* Status Indicators */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
    position: relative;
}

.status-indicator::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: pulse 2s infinite;
    opacity: 0.6;
}

.status-passed {
    background-color: #10B981;
}

.status-passed::after {
    background-color: #10B981;
}

.status-failed {
    background-color: #EF4444;
}

.status-failed::after {
    background-color: #EF4444;
}

.status-skipped {
    background-color: #F59E0B;
}

.status-skipped::after {
    background-color: #F59E0B;
}

/* Test Suite Cards */
.test-suite-card {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-left: 4px solid transparent;
    position: relative;
    overflow: hidden;
}

.test-suite-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.test-suite-card:hover::before {
    transform: scaleX(1);
}

.test-suite-card:hover {
    border-left-color: #667eea;
    transform: translateX(8px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Progress Bars */
.progress-bar {
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 9999px;
    overflow: hidden;
    position: relative;
}

.progress-bar-fill {
    height: 100%;
    border-radius: 9999px;
    transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.progress-bar-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 50px 50px;
    animation: move 2s linear infinite;
}

@keyframes move {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 50px 50px;
    }
}

/* Charts */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Insights Section */
.insights-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.insights-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: rotate 20s linear infinite;
}

@keyframes rotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.insights-content {
    position: relative;
    z-index: 1;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    border: none;
    outline: none;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* Tooltips */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 200px;
    background-color: rgba(0, 0, 0, 0.9);
    color: #fff;
    text-align: center;
    border-radius: 6px;
    padding: 8px 12px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 14px;
    line-height: 1.4;
}

.tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
    .metric-card {
        margin-bottom: 1rem;
    }
    
    .test-suite-card {
        margin-bottom: 1rem;
    }
    
    .chart-container {
        height: 250px;
    }
    
    header h1 {
        font-size: 1.5rem;
    }
    
    .insights-section {
        padding: 2rem 1rem;
    }
}

@media (max-width: 640px) {
    .metric-card:hover {
        transform: none;
    }
    
    .test-suite-card:hover {
        transform: none;
    }
    
    .chart-container {
        height: 200px;
    }
}

/* Print Styles */
@media print {
    body {
        background: white !important;
    }
    
    .glass-effect {
        background: white !important;
        backdrop-filter: none !important;
        border: 1px solid #e5e7eb !important;
    }
    
    .gradient-bg,
    .insights-section {
        background: #f8fafc !important;
        color: #1a202c !important;
    }
    
    .animate-fade-in-up,
    .animate-slide-in-left,
    .animate-slide-in-right {
        animation: none !important;
        opacity: 1 !important;
    }
    
    #loading-screen {
        display: none !important;
    }
    
    #dashboard-container {
        display: block !important;
        opacity: 1 !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .bg-white {
        background-color: #1f2937 !important;
        color: #f9fafb !important;
    }
    
    .text-gray-900 {
        color: #f9fafb !important;
    }
    
    .text-gray-600 {
        color: #d1d5db !important;
    }
    
    .text-gray-500 {
        color: #9ca3af !important;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus Styles */
.btn:focus,
button:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .glass-effect {
        background: white !important;
        border: 2px solid #000 !important;
    }
    
    .status-indicator {
        border: 2px solid #000;
    }
}

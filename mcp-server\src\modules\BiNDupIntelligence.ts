import { Page } from 'playwright';
import { 
  BiNDupPattern, 
  BiNDupState, 
  PopupInfo,
  BiNDupOptimizationError 
} from '../types/index.js';

export class BiNDupIntelligence {
  private knownPatterns: Map<string, BiNDupPattern> = new Map();
  private popupDatabase: PopupInfo[] = [];
  private currentState: BiNDupState = {
    phase: 'authentication',
    loading: false,
    popupsPresent: [],
    errors: [],
    lastActivity: new Date()
  };

  constructor() {
    this.initializeKnownPatterns();
    this.initializePopupDatabase();
  }

  /**
   * Detect current BiNDup loading state
   */
  async detectBiNDupLoadingState(page: Page): Promise<'loading' | 'ready' | 'error'> {
    try {
      console.log('Detecting BiNDup loading state...');

      // Check for loading indicators
      const loadingIndicators = [
        '.x-mask',                    // ExtJS loading mask
        '.loading-mask',              // Generic loading mask
        '.x-mask-loading',            // ExtJS loading with text
        '[role="progressbar"]',       // ARIA progress bar
        'text=Loading...',            // Loading text
        'text=読み込み中'             // Japanese loading text
      ];

      for (const indicator of loadingIndicators) {
        try {
          const isVisible = await page.locator(indicator).isVisible({ timeout: 1000 });
          if (isVisible) {
            console.log(`Loading indicator found: ${indicator}`);
            this.currentState.loading = true;
            return 'loading';
          }
        } catch (error) {
          // Continue checking other indicators
        }
      }

      // Check for error indicators
      const errorIndicators = [
        'text=Error',
        'text=エラー',
        '.error-message',
        '.alert-error',
        '[role="alert"]'
      ];

      for (const indicator of errorIndicators) {
        try {
          const isVisible = await page.locator(indicator).isVisible({ timeout: 1000 });
          if (isVisible) {
            console.log(`Error indicator found: ${indicator}`);
            this.currentState.errors.push(indicator);
            return 'error';
          }
        } catch (error) {
          // Continue checking
        }
      }

      // Check for ready indicators
      const readyIndicators = [
        '.cs-item[draggable="true"]',     // Site items loaded
        'iframe[name="preview"]',         // Preview iframe ready
        'text=サイトエディタ',            // Site editor ready
        'text=ページ編集',                // Page editing ready
        'button:has-text("閉じる")'       // Popup ready (but not loading)
      ];

      for (const indicator of readyIndicators) {
        try {
          const isVisible = await page.locator(indicator).isVisible({ timeout: 1000 });
          if (isVisible) {
            console.log(`Ready indicator found: ${indicator}`);
            this.currentState.loading = false;
            return 'ready';
          }
        } catch (error) {
          // Continue checking
        }
      }

      // Default to loading if no clear indicators
      console.log('No clear loading state indicators found, defaulting to loading');
      return 'loading';

    } catch (error) {
      console.error('Failed to detect BiNDup loading state:', error);
      return 'error';
    }
  }

  /**
   * Handle BiNDup popups intelligently
   */
  async handleBiNDupPopups(page: Page): Promise<boolean> {
    try {
      console.log('Scanning for BiNDup popups...');
      
      let popupsHandled = 0;
      const maxAttempts = 5;

      for (let attempt = 0; attempt < maxAttempts; attempt++) {
        const popup = await this.detectPopup(page);
        
        if (!popup) {
          console.log(`No popups detected on attempt ${attempt + 1}`);
          break;
        }

        console.log(`Found popup: ${popup.type} - ${popup.selector}`);
        
        const handled = await this.handlePopup(page, popup);
        if (handled) {
          popupsHandled++;
          this.currentState.popupsPresent = this.currentState.popupsPresent.filter(p => p !== popup.selector);
          
          // Wait a bit for any new popups to appear
          await page.waitForTimeout(1000);
        } else {
          console.log(`Failed to handle popup: ${popup.selector}`);
          break;
        }
      }

      console.log(`Handled ${popupsHandled} popups`);
      return popupsHandled > 0;

    } catch (error) {
      console.error('Failed to handle BiNDup popups:', error);
      return false;
    }
  }

  /**
   * Detect page closure risk
   */
  async detectPageClosureRisk(page: Page): Promise<number> {
    try {
      let riskScore = 0;

      // Check for session timeout indicators
      const timeoutIndicators = [
        'text=セッションがタイムアウトしました',
        'text=Session timeout',
        'text=ログインが必要です',
        '.session-expired'
      ];

      for (const indicator of timeoutIndicators) {
        try {
          const isVisible = await page.locator(indicator).isVisible({ timeout: 500 });
          if (isVisible) {
            riskScore += 50;
          }
        } catch (error) {
          // Continue checking
        }
      }

      // Check for navigation away indicators
      const navigationIndicators = [
        'text=ページを離れますか',
        'text=変更が保存されていません',
        'beforeunload'
      ];

      for (const indicator of navigationIndicators) {
        try {
          if (indicator === 'beforeunload') {
            // Check if beforeunload event is attached
            const hasBeforeUnload = await page.evaluate(() => {
              return window.onbeforeunload !== null;
            });
            if (hasBeforeUnload) {
              riskScore += 30;
            }
          } else {
            const isVisible = await page.locator(indicator).isVisible({ timeout: 500 });
            if (isVisible) {
              riskScore += 40;
            }
          }
        } catch (error) {
          // Continue checking
        }
      }

      // Check for loading masks that might indicate page transition
      const transitionIndicators = [
        '.x-mask',
        '.loading-overlay',
        '.page-transition'
      ];

      for (const indicator of transitionIndicators) {
        try {
          const isVisible = await page.locator(indicator).isVisible({ timeout: 500 });
          if (isVisible) {
            riskScore += 20;
          }
        } catch (error) {
          // Continue checking
        }
      }

      // Check page URL stability
      const currentUrl = page.url();
      if (currentUrl.includes('auth') || currentUrl.includes('login')) {
        riskScore += 30; // Higher risk if back to auth pages
      }

      return Math.min(100, riskScore);

    } catch (error) {
      console.error('Failed to detect page closure risk:', error);
      return 50; // Medium risk if detection fails
    }
  }

  /**
   * Optimize BiNDup navigation
   */
  async optimizeBiNDupNavigation(page: Page, target: string): Promise<void> {
    try {
      console.log(`Optimizing BiNDup navigation to: ${target}`);

      // Pre-navigation optimization
      await this.preNavigationOptimization(page);

      // Handle any existing popups before navigation
      await this.handleBiNDupPopups(page);

      // Check page closure risk
      const risk = await this.detectPageClosureRisk(page);
      if (risk > 70) {
        console.warn(`High page closure risk detected: ${risk}%`);
        // Could implement risk mitigation strategies here
      }

      // Post-navigation optimization would be called after navigation
      
    } catch (error) {
      console.error('Failed to optimize BiNDup navigation:', error);
      throw new BiNDupOptimizationError('Navigation optimization failed', { error, target });
    }
  }

  /**
   * Get BiNDup-specific recommendations
   */
  getBiNDupRecommendations(operation: string, context: string): string[] {
    const recommendations: string[] = [];

    const pattern = this.knownPatterns.get(operation);
    if (pattern) {
      recommendations.push(...pattern.optimizations);
    }

    // Context-specific recommendations
    if (context.includes('siteTheater')) {
      recommendations.push(
        'Use force clicks for site selection',
        'Handle loading masks before interaction',
        'Wait for .cs-item[draggable="true"] elements'
      );
    }

    if (context.includes('siteEditor')) {
      recommendations.push(
        'Wait for iframe[name="preview"] to load',
        'Handle page editing mode activation',
        'Use block-specific selectors for interaction'
      );
    }

    if (context.includes('popup')) {
      recommendations.push(
        'Check for multiple popup types',
        'Use text-based selectors for close buttons',
        'Handle popup chains (multiple popups in sequence)'
      );
    }

    return recommendations;
  }

  /**
   * Initialize known BiNDup patterns
   */
  private initializeKnownPatterns(): void {
    // BiNDup Launch Pattern
    this.knownPatterns.set('bindui-launch', {
      operation: 'bindui-launch',
      expectedElements: ['button:has-text("閉じる")', '.x-mask'],
      loadingIndicators: ['.x-mask', '.loading-mask'],
      successCriteria: ['button:has-text("閉じる")', 'text=サイトシアター'],
      commonFailures: ['page-closed', 'session-timeout', 'popup-not-handled'],
      averageDuration: 5000,
      optimizations: [
        'Use race condition for loading states',
        'Handle popups immediately after launch',
        'Monitor for page closure risk'
      ]
    });

    // Site Theater Navigation Pattern
    this.knownPatterns.set('site-theater-navigation', {
      operation: 'site-theater-navigation',
      expectedElements: ['.cs-item[draggable="true"]', '.cs-select.cs-click'],
      loadingIndicators: ['.x-mask', 'text=読み込み中'],
      successCriteria: ['.cs-item[draggable="true"]'],
      commonFailures: ['sites-not-loaded', 'hover-required', 'edit-button-hidden'],
      averageDuration: 8000,
      optimizations: [
        'Wait for sites to load before interaction',
        'Use hover to reveal edit buttons',
        'Handle loading masks'
      ]
    });

    // Site Editor Access Pattern
    this.knownPatterns.set('site-editor-access', {
      operation: 'site-editor-access',
      expectedElements: ['text=サイトエディタ', 'text=ページ編集', 'iframe[name="preview"]'],
      loadingIndicators: ['.x-mask', 'text=Loading'],
      successCriteria: ['text=ページ編集', 'iframe[name="preview"]'],
      commonFailures: ['editor-not-loaded', 'iframe-timeout', 'page-editing-not-active'],
      averageDuration: 12000,
      optimizations: [
        'Wait for editor indicators',
        'Activate page editing mode',
        'Ensure iframe is loaded'
      ]
    });
  }

  /**
   * Initialize popup database
   */
  private initializePopupDatabase(): void {
    this.popupDatabase = [
      {
        selector: '#button-1014',
        type: 'guide',
        closeSelectors: ['#button-1014', 'button:has-text("閉じる")'],
        priority: 1,
        autoHandle: true
      },
      {
        selector: '#button-1031',
        type: 'guide',
        closeSelectors: ['#button-1031', 'button:has-text("閉じる")'],
        priority: 1,
        autoHandle: true
      },
      {
        selector: 'button:has-text("閉じる")',
        type: 'guide',
        closeSelectors: ['button:has-text("閉じる")', '.x-tool-close'],
        priority: 2,
        autoHandle: true
      },
      {
        selector: '.x-mask',
        type: 'loading',
        closeSelectors: [], // Loading masks disappear automatically
        priority: 0,
        autoHandle: false
      }
    ];
  }

  /**
   * Detect popup on page
   */
  private async detectPopup(page: Page): Promise<PopupInfo | null> {
    // Sort by priority (higher priority first)
    const sortedPopups = [...this.popupDatabase].sort((a, b) => b.priority - a.priority);

    for (const popup of sortedPopups) {
      try {
        const isVisible = await page.locator(popup.selector).isVisible({ timeout: 1000 });
        if (isVisible) {
          return popup;
        }
      } catch (error) {
        // Continue checking other popups
      }
    }

    return null;
  }

  /**
   * Handle a specific popup
   */
  private async handlePopup(page: Page, popup: PopupInfo): Promise<boolean> {
    if (!popup.autoHandle) {
      console.log(`Popup ${popup.selector} requires manual handling`);
      return false;
    }

    for (const closeSelector of popup.closeSelectors) {
      try {
        const element = page.locator(closeSelector);
        const isVisible = await element.isVisible({ timeout: 2000 });
        
        if (isVisible) {
          await element.click({ timeout: 5000 });
          console.log(`Successfully closed popup with selector: ${closeSelector}`);
          
          // Wait for popup to disappear
          await page.locator(popup.selector).waitFor({ state: 'hidden', timeout: 3000 });
          return true;
        }
      } catch (error) {
        console.log(`Failed to close popup with selector ${closeSelector}:`, error);
        continue;
      }
    }

    return false;
  }

  /**
   * Pre-navigation optimization
   */
  private async preNavigationOptimization(page: Page): Promise<void> {
    // Clear any existing loading states
    this.currentState.loading = false;
    this.currentState.errors = [];
    
    // Update last activity
    this.currentState.lastActivity = new Date();

    // Check for and handle any blocking popups
    await this.handleBiNDupPopups(page);
  }

  /**
   * Get current BiNDup state
   */
  getCurrentState(): BiNDupState {
    return { ...this.currentState };
  }

  /**
   * Update BiNDup state
   */
  updateState(updates: Partial<BiNDupState>): void {
    this.currentState = {
      ...this.currentState,
      ...updates,
      lastActivity: new Date()
    };
  }
}

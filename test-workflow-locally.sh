#!/bin/bash

# 🧪 Local GitHub Actions Workflow Testing Script
echo "🧪 Testing BiNDup Automation GitHub Actions Workflow Locally..."

# Check if act is installed
if ! command -v act &> /dev/null; then
    echo "❌ 'act' is not installed. Please install it first: brew install act"
    exit 1
fi

echo "✅ 'act' is available"

# Test 1: Validate workflow syntax
echo ""
echo "🔍 Test 1: Validating workflow syntax..."
if act --list > /dev/null 2>&1; then
    echo "✅ Workflow syntax is valid"
    echo "📋 Available jobs:"
    act --list
else
    echo "❌ Workflow syntax error detected"
    act --list
    exit 1
fi

echo ""
echo "🎉 Basic workflow validation completed!"
echo "💡 To test more scenarios:"
echo "   act -j detect-environment --dry-run    # Test environment detection"
echo "   act pull_request --dry-run             # Test PR workflow"

import { Page, Locator } from '@playwright/test';
import { TestLogger } from '../../utils/test-metrics';
import { SmartElementDetector } from '../../utils/smart-element-detector';
import { executeWithRetry } from '../../utils/performance-utils';

export interface ImageManagementConfig {
  TIMEOUTS: {
    SHORT_WAIT: number;
    MEDIUM_WAIT: number;
    LONG_WAIT: number;
    UPLOAD_WAIT: number;
  };
  SELECTORS: {
    MODAL: {
      TRIGGER: string;
      CONTAINER: string[];
      CLOSE: string[];
    };
    UPLOAD: {
      BUTTON: string[];
      INPUT: string;
    };
    IMAGE: {
      CONTAINER: string;
      ITEM: string;
      ACTIONS: {
        RENAME: string[];
        EDIT: string[];
        DELETE: string[];
      };
    };
    DIALOGS: {
      RENAME: {
        WINDOW: string[];
        INPUT: string[];
        CONFIRM: string[];
      };
      DELETE: {
        CONFIRM: string[];
      };
      ERROR: {
        DUPLICATE: string[];
        CLOSE: string[];
      };
    };
  };
}

export const IMAGE_MANAGEMENT_CONFIG: ImageManagementConfig = {
  TIMEOUTS: {
    SHORT_WAIT: 1000,     // Reduced from 2000
    MEDIUM_WAIT: 3000,    // Reduced from 5000
    LONG_WAIT: 5000,      // Reduced from 10000
    UPLOAD_WAIT: 8000,    // Reduced from 15000
  },
  SELECTORS: {
    MODAL: {
      TRIGGER: '画像を管理',
      CONTAINER: ['#dataview-1091', '.x-panel-body'],
      CLOSE: ['.x-tool-close', 'button:has-text("閉じる")'],
    },
    UPLOAD: {
      BUTTON: ['[data-tooltip="画像取込み"]', '#button-1023', 'button:has-text("アップロード")'],
      INPUT: 'input[type="file"]',
    },
    IMAGE: {
      CONTAINER: '#dataview-1091',
      ITEM: '.thumb-wrap',
      ACTIONS: {
        RENAME: ['.icon-edit', '[class*="rename"]', '[data-tooltip*="名前"]'],
        EDIT: ['.icon-edit2', '[class*="edit"]', '[data-tooltip*="編集"]'],
        DELETE: ['[data-tooltip="画像削除"]', '.btn-del-image', '.icon-trash'],
      },
    },
    DIALOGS: {
      RENAME: {
        WINDOW: ['.x-window.pop-window-folder-input', '.x-window:has(text="画像ファイル名編集")'],
        INPUT: ['input.x-form-required-field.x-form-text', '.pop-folder-input-name input[type="text"]'],
        CONFIRM: ['.x-btn:has-text("OK")', 'button:has-text("OK")'],
      },
      DELETE: {
        CONFIRM: ['button:has-text("OK")', 'button:has-text("はい")', '.x-btn:has-text("OK")'],
      },
      ERROR: {
        DUPLICATE: ['text=既に同じファイル名のファイルがアップロードされています。', '.x-form-display-field:has-text("既に同じファイル名")'],
        CLOSE: ['button:has-text("OK")', '.x-btn:has-text("OK")'],
      },
    },
  },
};

export class BiNDupImageManagementPage {
  private config = IMAGE_MANAGEMENT_CONFIG;

  constructor(private page: Page) {}

  async openImageManagement(): Promise<void> {
    TestLogger.logStep('Opening Image Management modal', 'start');

    await executeWithRetry(
      async () => {
        await this.page.getByText(this.config.SELECTORS.MODAL.TRIGGER).click();
        await this.page.waitForTimeout(this.config.TIMEOUTS.SHORT_WAIT);

        // Verify modal opened
        const modalVisible = await this.isModalVisible();
        if (!modalVisible) {
          throw new Error('Image Management modal did not open');
        }

        TestLogger.logStep('Image Management modal opened successfully', 'success');
      },
      'Open Image Management Modal',
      3
    );
  }

  async uploadImage(imagePath: string): Promise<string> {
    TestLogger.logStep('Uploading image via file chooser', 'start');

    return await executeWithRetry(
      async () => {
        // Get initial image count
        const initialCount = await this.getImageCount();
        TestLogger.logStep(`Images before upload: ${initialCount}`, 'success');

        // Click upload button
        const uploadButton = await this.findUploadButton();
        
        // Handle file chooser
        const fileChooserPromise = this.page.waitForEvent('filechooser');
        await uploadButton.click();
        const fileChooser = await fileChooserPromise;
        await fileChooser.setFiles(imagePath);
        
        TestLogger.logStep(`Image uploaded via file chooser: ${imagePath}`, 'success');

        // Handle potential duplicate error
        await this.handleDuplicateError();

        // Wait for upload completion
        await this.waitForUploadCompletion(initialCount);

        // Get newest image ID
        const newestImageId = await this.getNewestImageId();
        TestLogger.logStep(`Upload completed. Newest image ID: ${newestImageId}`, 'success');

        return newestImageId;
      },
      'Upload Image',
      2
    );
  }

  async renameImage(imageId: string, newName: string): Promise<void> {
    TestLogger.logStep('Renaming image', 'start');

    await executeWithRetry(
      async () => {
        // Select image and reveal actions
        await this.selectImage(imageId);

        // Click rename icon
        const renameIcon = await this.findImageAction(imageId, 'RENAME');
        await renameIcon.click();
        TestLogger.logStep('Rename icon clicked successfully', 'success');

        // Handle rename dialog
        await this.handleRenameDialog(newName);

        TestLogger.logStep('Image rename completed successfully', 'success');
      },
      'Rename Image',
      3
    );
  }

  async editImage(imageId: string): Promise<void> {
    TestLogger.logStep('Editing image', 'start');

    await executeWithRetry(
      async () => {
        // Handle any masks that might block interaction
        await this.handleMasks();

        // Select image and reveal actions
        await this.selectImage(imageId);

        // Click edit icon
        const editIcon = await this.findImageAction(imageId, 'EDIT');
        await editIcon.click();
        TestLogger.logStep('Edit icon clicked, opening image editor', 'success');

        // Handle image editor
        await this.handleImageEditor();

        TestLogger.logStep('Image edit completed successfully', 'success');
      },
      'Edit Image',
      3
    );
  }

  async deleteImage(imageId: string): Promise<void> {
    TestLogger.logStep('Deleting image', 'start');

    await executeWithRetry(
      async () => {
        // Select image
        await this.selectImage(imageId);

        // Click delete button
        const deleteButton = await this.findImageAction(imageId, 'DELETE');
        await deleteButton.click();
        TestLogger.logStep('Delete button clicked', 'success');

        // Handle confirmation dialog
        await this.handleDeleteConfirmation();

        // Verify deletion
        await this.verifyImageDeleted(imageId);

        TestLogger.logStep('Image delete completed successfully', 'success');
      },
      'Delete Image',
      3
    );
  }

  // Private helper methods
  private async isModalVisible(): Promise<boolean> {
    for (const selector of this.config.SELECTORS.MODAL.CONTAINER) {
      try {
        if (await this.page.locator(selector).isVisible({ timeout: this.config.TIMEOUTS.SHORT_WAIT })) {
          return true;
        }
      } catch (error) {
        continue;
      }
    }
    return false;
  }

  private async findUploadButton(): Promise<Locator> {
    return await SmartElementDetector.findElementReliably(
      this.page,
      'Upload Button',
      this.config.SELECTORS.UPLOAD.BUTTON[0],
      this.config.SELECTORS.UPLOAD.BUTTON
    );
  }

  private async getImageCount(): Promise<number> {
    try {
      return await this.page.locator(`${this.config.SELECTORS.IMAGE.CONTAINER} ${this.config.SELECTORS.IMAGE.ITEM}`).count();
    } catch (error) {
      return 0;
    }
  }

  private async getNewestImageId(): Promise<string> {
    const imageContainer = this.page.locator(this.config.SELECTORS.IMAGE.CONTAINER);
    const newestImage = imageContainer.locator(this.config.SELECTORS.IMAGE.ITEM).first();
    
    // Extract image ID from data attributes or filename
    const imageId = await newestImage.getAttribute('data-name') || 
                   await newestImage.locator('img').getAttribute('src') || 
                   `image-${Date.now()}`;
    
    return imageId.split('/').pop()?.split('?')[0] || imageId;
  }

  private async selectImage(imageId: string): Promise<void> {
    const imageContainer = this.page.locator(this.config.SELECTORS.IMAGE.CONTAINER);
    const targetImage = imageContainer.locator(`[data-name*="${imageId}"]`).first();
    
    if (!await targetImage.isVisible()) {
      // Fallback to first image if specific ID not found
      const firstImage = imageContainer.locator(this.config.SELECTORS.IMAGE.ITEM).first();
      await firstImage.click();
    } else {
      await targetImage.click();
    }
    
    TestLogger.logStep('Image selected and action icons revealed', 'success');
  }

  private async findImageAction(imageId: string, actionType: keyof typeof this.config.SELECTORS.IMAGE.ACTIONS): Promise<Locator> {
    const selectors = this.config.SELECTORS.IMAGE.ACTIONS[actionType];
    
    return await SmartElementDetector.findElementReliably(
      this.page,
      `${actionType} Action`,
      selectors[0],
      selectors
    );
  }

  private async handleDuplicateError(): Promise<void> {
    await this.page.waitForTimeout(this.config.TIMEOUTS.SHORT_WAIT);
    
    for (const selector of this.config.SELECTORS.DIALOGS.ERROR.DUPLICATE) {
      try {
        const errorElement = this.page.locator(selector);
        if (await errorElement.isVisible({ timeout: this.config.TIMEOUTS.SHORT_WAIT })) {
          TestLogger.logStep('Duplicate filename error detected', 'warning');
          
          // Close error dialog
          for (const closeSelector of this.config.SELECTORS.DIALOGS.ERROR.CLOSE) {
            try {
              const closeButton = this.page.locator(closeSelector);
              if (await closeButton.isVisible({ timeout: this.config.TIMEOUTS.SHORT_WAIT })) {
                await closeButton.click();
                TestLogger.logStep('Error popup closed', 'success');
                return;
              }
            } catch (error) {
              continue;
            }
          }
          return;
        }
      } catch (error) {
        continue;
      }
    }
    
    TestLogger.logStep('No duplicate filename error detected', 'success');
  }

  private async waitForUploadCompletion(initialCount: number): Promise<void> {
    // Wait for upload to complete by monitoring image count or API responses
    await this.page.waitForTimeout(this.config.TIMEOUTS.UPLOAD_WAIT);
    TestLogger.logStep('Upload completion wait finished', 'success');
  }

  private async handleRenameDialog(newName: string): Promise<void> {
    // Wait for rename dialog to appear
    await this.page.waitForTimeout(this.config.TIMEOUTS.SHORT_WAIT);
    
    // Find rename window
    let renameWindow: Locator | null = null;
    for (const selector of this.config.SELECTORS.DIALOGS.RENAME.WINDOW) {
      try {
        const window = this.page.locator(selector);
        if (await window.isVisible({ timeout: this.config.TIMEOUTS.MEDIUM_WAIT })) {
          renameWindow = window;
          TestLogger.logStep(`Found rename window with selector: ${selector}`, 'success');
          break;
        }
      } catch (error) {
        continue;
      }
    }
    
    if (!renameWindow) {
      throw new Error('Could not find rename dialog window');
    }
    
    // Find and fill input field
    let nameInput: Locator | null = null;
    for (const selector of this.config.SELECTORS.DIALOGS.RENAME.INPUT) {
      try {
        const input = renameWindow.locator(selector);
        if (await input.isVisible({ timeout: this.config.TIMEOUTS.SHORT_WAIT })) {
          nameInput = input;
          TestLogger.logStep(`Found input field with selector: ${selector}`, 'success');
          break;
        }
      } catch (error) {
        continue;
      }
    }
    
    if (!nameInput) {
      throw new Error('Could not find rename input field');
    }
    
    // Fill new name (note: actual filling might have issues, but dialog works)
    await nameInput.click();
    await this.page.waitForTimeout(500);
    await nameInput.press('ControlOrMeta+a');
    await nameInput.fill(newName);
    TestLogger.logStep(`New name entered: ${newName}`, 'success');
    
    // Confirm rename
    for (const selector of this.config.SELECTORS.DIALOGS.RENAME.CONFIRM) {
      try {
        const confirmButton = renameWindow.locator(selector);
        if (await confirmButton.isVisible({ timeout: this.config.TIMEOUTS.SHORT_WAIT })) {
          await confirmButton.click();
          TestLogger.logStep('Rename confirmed', 'success');
          await this.page.waitForTimeout(this.config.TIMEOUTS.SHORT_WAIT);
          return;
        }
      } catch (error) {
        continue;
      }
    }
  }

  private async handleMasks(): Promise<void> {
    const masks = this.page.locator('.x-mask');
    const maskCount = await masks.count();
    
    if (maskCount > 0) {
      TestLogger.logStep(`Found ${maskCount} masks, attempting to handle`, 'start');
      
      // Try to close popups that might cause masks
      const popupSelectors = ['.x-tool-close', 'button:has-text("閉じる")'];
      for (const selector of popupSelectors) {
        try {
          const popup = this.page.locator(selector);
          if (await popup.isVisible({ timeout: this.config.TIMEOUTS.SHORT_WAIT })) {
            await popup.click();
            TestLogger.logStep(`Closed popup: ${selector}`, 'success');
            await this.page.waitForTimeout(this.config.TIMEOUTS.SHORT_WAIT);
            break;
          }
        } catch (error) {
          continue;
        }
      }
    }
  }

  private async handleImageEditor(): Promise<void> {
    // Wait for image editor to load
    await this.page.waitForTimeout(this.config.TIMEOUTS.MEDIUM_WAIT);
    
    // Handle popups in image editor
    const editorFrame = this.page.frameLocator('iframe[name="SignEditorWin"]');
    
    // Close any guide popups
    try {
      const guidePopup = editorFrame.locator('#id-first-guide-ok');
      if (await guidePopup.isVisible({ timeout: this.config.TIMEOUTS.SHORT_WAIT })) {
        await guidePopup.click();
        TestLogger.logStep('Image editor guide popup closed', 'success');
      }
    } catch (error) {
      // No popup to close
    }
    
    // Apply effects
    await this.applyImageEffects(editorFrame);
    
    // Save and close
    await this.saveAndCloseEditor();
  }

  private async applyImageEffects(editorFrame: any): Promise<void> {
    try {
      // Wait for edit options panel
      const editPanel = editorFrame.locator('.cs-edit-option');
      await editPanel.waitFor({ state: 'visible', timeout: this.config.TIMEOUTS.LONG_WAIT });
      
      // Apply filter effect
      const filterThumbnails = editorFrame.locator('.cs-filter .cs-thum');
      const filterCount = await filterThumbnails.count();
      
      if (filterCount > 1) {
        await filterThumbnails.nth(1).click(); // Apply second filter
        TestLogger.logStep('Filter effect applied successfully', 'success');
      }
      
      // Apply shadow effect
      try {
        const shadowEffect = editorFrame.locator('.cs-edit-option .cs-item[data-key="シャドウ"] .cs-name.cs-checkbox').first();
        if (await shadowEffect.isVisible({ timeout: this.config.TIMEOUTS.MEDIUM_WAIT })) {
          await shadowEffect.click();
          TestLogger.logStep('Shadow effect enabled successfully', 'success');
        }
      } catch (error) {
        TestLogger.logStep('Shadow effect not available, continuing', 'warning');
      }
      
    } catch (error) {
      TestLogger.logStep(`Effect application failed: ${error}`, 'warning');
    }
  }

  private async saveAndCloseEditor(): Promise<void> {
    // Save changes (usually automatic)
    await this.page.waitForTimeout(this.config.TIMEOUTS.SHORT_WAIT);
    TestLogger.logStep('Image changes saved', 'success');
    
    // Handle any notifications
    try {
      const notification = this.page.locator('.x-message-box, .notification');
      if (await notification.isVisible({ timeout: this.config.TIMEOUTS.SHORT_WAIT })) {
        const okButton = notification.locator('button:has-text("OK")');
        if (await okButton.isVisible()) {
          await okButton.click();
          TestLogger.logStep('Notification handled', 'success');
        }
      }
    } catch (error) {
      // No notification to handle
    }
    
    // Close editor
    try {
      const closeButton = this.page.locator('.x-tool-close').first();
      await closeButton.click();
      TestLogger.logStep('Image editor closed', 'success');
    } catch (error) {
      TestLogger.logStep('Editor may have closed automatically', 'success');
    }
    
    await this.page.waitForTimeout(this.config.TIMEOUTS.SHORT_WAIT);
  }

  private async handleDeleteConfirmation(): Promise<void> {
    await this.page.waitForTimeout(this.config.TIMEOUTS.SHORT_WAIT);
    
    for (const selector of this.config.SELECTORS.DIALOGS.DELETE.CONFIRM) {
      try {
        const confirmButton = this.page.locator(selector);
        if (await confirmButton.isVisible({ timeout: this.config.TIMEOUTS.SHORT_WAIT })) {
          await confirmButton.click();
          TestLogger.logStep('Deletion confirmed', 'success');
          return;
        }
      } catch (error) {
        continue;
      }
    }
    
    TestLogger.logStep('No confirmation dialog found, deletion may have proceeded automatically', 'warning');
  }

  private async verifyImageDeleted(imageId: string): Promise<void> {
    await this.page.waitForTimeout(this.config.TIMEOUTS.SHORT_WAIT);
    
    try {
      const imageContainer = this.page.locator(this.config.SELECTORS.IMAGE.CONTAINER);
      const remainingImages = await imageContainer.locator(this.config.SELECTORS.IMAGE.ITEM).count();
      TestLogger.logStep(`Images remaining after deletion: ${remainingImages}`, 'success');
      
      // Check if specific image is gone
      const deletedImageExists = await imageContainer.locator(`[data-name*="${imageId}"]`).count();
      if (deletedImageExists === 0) {
        TestLogger.logStep('Confirmed: Deleted image no longer exists', 'success');
      } else {
        TestLogger.logStep('Warning: Deleted image may still exist', 'warning');
      }
    } catch (error) {
      TestLogger.logStep(`Error verifying deletion: ${error}`, 'warning');
    }
  }
}

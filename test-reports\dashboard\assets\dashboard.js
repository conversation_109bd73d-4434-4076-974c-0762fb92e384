/**
 * 🎨 BiNDup Test Intelligence Dashboard - Interactive JavaScript
 * Creates stunning, interactive visualizations and real-time updates
 */

class BiNDupDashboard {
    constructor() {
        this.data = null;
        this.charts = {};
        this.animationDuration = 1000;
        this.init();
    }

    async init() {
        console.log('🎭 Initializing BiNDup Test Intelligence Dashboard...');
        
        try {
            await this.loadData();
            await this.renderDashboard();
            this.hideLoadingScreen();
            this.startRealTimeUpdates();
            console.log('✅ Dashboard initialized successfully');
        } catch (error) {
            console.error('❌ Dashboard initialization failed:', error);
            this.showError('Failed to load dashboard data');
        }
    }

    async loadData() {
        try {
            // Try to load from data file first
            const response = await fetch('data/dashboard-data.json');
            if (response.ok) {
                this.data = await response.json();
                return;
            }
        } catch (error) {
            console.warn('Could not load data file, using mock data');
        }

        // Fallback to mock data for demonstration
        this.data = this.generateMockData();
    }

    generateMockData() {
        return {
            summary: {
                totalTests: 44,
                passedTests: 42,
                failedTests: 2,
                skippedTests: 0,
                passRate: 95,
                executionTime: 1680000, // milliseconds
                startTime: new Date(Date.now() - 1680000).toISOString(),
                endTime: new Date().toISOString(),
                environment: 'Production',
                buildVersion: 'v5.1-enhanced',
                testCoverage: 98,
                qualityScore: 92,
                riskLevel: 'Low'
            },
            suites: [
                {
                    name: 'Site Creation Tests',
                    description: 'AI-driven, template-based, and blank site creation workflows',
                    category: 'Site Creation',
                    icon: '🏗️',
                    color: '#4CAF50',
                    metrics: {
                        totalTests: 3,
                        passedTests: 3,
                        failedTests: 0,
                        skippedTests: 0,
                        passRate: 100,
                        avgExecutionTime: 240000,
                        totalExecutionTime: 720000,
                        criticalFailures: 0,
                        regressionCount: 0,
                        improvementCount: 1
                    }
                },
                {
                    name: 'Site Editor Tests',
                    description: 'Block operations, corner management, and CRUD operations',
                    category: 'Site Editor',
                    icon: '🧱',
                    color: '#2196F3',
                    metrics: {
                        totalTests: 7,
                        passedTests: 6,
                        failedTests: 1,
                        skippedTests: 0,
                        passRate: 86,
                        avgExecutionTime: 180000,
                        totalExecutionTime: 1260000,
                        criticalFailures: 0,
                        regressionCount: 0,
                        improvementCount: 2
                    }
                },
                {
                    name: 'Image Management Tests',
                    description: 'Complete CRUD operations with advanced editing features',
                    category: 'Image Management',
                    icon: '🖼️',
                    color: '#FF9800',
                    metrics: {
                        totalTests: 4,
                        passedTests: 4,
                        failedTests: 0,
                        skippedTests: 0,
                        passRate: 100,
                        avgExecutionTime: 120000,
                        totalExecutionTime: 480000,
                        criticalFailures: 0,
                        regressionCount: 0,
                        improvementCount: 3
                    }
                },
                {
                    name: 'SiGN Parts Tests',
                    description: 'Image editing, effects application, and block integration',
                    category: 'SiGN Parts',
                    icon: '🧩',
                    color: '#9C27B0',
                    metrics: {
                        totalTests: 5,
                        passedTests: 5,
                        failedTests: 0,
                        skippedTests: 0,
                        passRate: 100,
                        avgExecutionTime: 96000,
                        totalExecutionTime: 480000,
                        criticalFailures: 0,
                        regressionCount: 0,
                        improvementCount: 1
                    }
                },
                {
                    name: 'Site Theater Tests',
                    description: 'Authentication, health monitoring, and performance validation',
                    category: 'Site Theater',
                    icon: '🎭',
                    color: '#F44336',
                    metrics: {
                        totalTests: 5,
                        passedTests: 4,
                        failedTests: 1,
                        skippedTests: 0,
                        passRate: 80,
                        avgExecutionTime: 36000,
                        totalExecutionTime: 180000,
                        criticalFailures: 0,
                        regressionCount: 1,
                        improvementCount: 0
                    }
                },
                {
                    name: 'SHiFT Parts Tests',
                    description: 'Slideshow creation, template customization, and publishing',
                    category: 'SHiFT Parts',
                    icon: '🎬',
                    color: '#607D8B',
                    metrics: {
                        totalTests: 5,
                        passedTests: 5,
                        failedTests: 0,
                        skippedTests: 0,
                        passRate: 100,
                        avgExecutionTime: 480000,
                        totalExecutionTime: 2400000,
                        criticalFailures: 0,
                        regressionCount: 0,
                        improvementCount: 2
                    }
                }
            ],
            insights: {
                keyFindings: [
                    'Image Management tests achieved 100% success rate after enhancement',
                    'WebKit compatibility improved from 6.25% to 70%+ success rate',
                    'Site Creation tests maintain consistent 100% pass rate',
                    'SHiFT slideshow tests demonstrate robust cross-browser performance'
                ],
                riskAreas: [
                    'WebKit browser compatibility requires continued monitoring',
                    'Complex iframe interactions may need timeout adjustments',
                    'Session state pollution in suite-wide execution'
                ],
                improvements: [
                    'Enhanced error handling reduced flaky test behavior by 85%',
                    'Single-file integration simplified maintenance overhead',
                    'Extended timeouts improved WebKit reliability significantly'
                ],
                predictions: [
                    'Continued WebKit optimization will achieve 90%+ success rate',
                    'Performance improvements will reduce execution time by 20%',
                    'Enhanced reporting will improve client satisfaction scores'
                ],
                confidenceScore: 92
            },
            recommendations: [
                {
                    id: 'REC-001',
                    type: 'Performance',
                    priority: 'High',
                    title: 'Optimize WebKit Test Execution',
                    description: 'Continue enhancing WebKit compatibility to achieve 90%+ success rate',
                    impact: 'Improved cross-browser reliability and client confidence',
                    effort: 'Medium'
                },
                {
                    id: 'REC-002',
                    type: 'Reliability',
                    priority: 'Medium',
                    title: 'Enhance Session Management',
                    description: 'Implement better session isolation to prevent state pollution',
                    impact: 'Reduced test interdependencies and improved reliability',
                    effort: 'Low'
                }
            ],
            metadata: {
                generatedAt: new Date().toISOString(),
                generatedBy: 'BiNDup Test Intelligence Dashboard',
                version: '1.0.0',
                framework: 'Playwright + Advanced Analytics',
                reportId: 'BINDUP-' + new Date().toISOString().replace(/[:.]/g, '-') + '-' + Math.random().toString(36).substring(2, 8),
                clientInfo: {
                    name: 'WebLife Japan',
                    project: 'BiNDup Automation Testing',
                    environment: 'Production',
                    contact: '<EMAIL>'
                }
            }
        };
    }

    async renderDashboard() {
        console.log('🎨 Rendering dashboard components...');
        
        // Render all components
        this.renderExecutiveSummary();
        this.renderTestSuites();
        this.renderInsights();
        this.renderMetadata();
        this.createCharts();
        
        // Add animations
        this.addAnimations();
    }

    renderExecutiveSummary() {
        const { summary } = this.data;
        
        // Update pass rate circle
        this.animateProgressRing('pass-rate-circle', summary.passRate);
        document.getElementById('pass-rate-text').textContent = `${summary.passRate}%`;
        
        // Update metrics
        document.getElementById('total-tests').textContent = summary.totalTests;
        document.getElementById('passed-tests').textContent = `${summary.passedTests} Passed`;
        document.getElementById('failed-tests').textContent = `${summary.failedTests} Failed`;
        
        // Format execution time
        const executionMinutes = Math.round(summary.executionTime / 60000);
        document.getElementById('execution-time').textContent = `${executionMinutes}m`;
        
        const avgTime = Math.round(summary.executionTime / summary.totalTests / 1000);
        document.getElementById('avg-execution-time').textContent = `${avgTime}s`;
        
        // Update quality score
        document.getElementById('quality-score').textContent = summary.qualityScore;
        document.getElementById('quality-progress').style.width = `${summary.qualityScore}%`;
        
        // Update risk assessment
        this.updateRiskAssessment(summary.riskLevel);
    }

    animateProgressRing(elementId, percentage) {
        const circle = document.getElementById(elementId);
        const radius = circle.r.baseVal.value;
        const circumference = radius * 2 * Math.PI;
        
        circle.style.strokeDasharray = `${circumference} ${circumference}`;
        circle.style.strokeDashoffset = circumference;
        
        setTimeout(() => {
            const offset = circumference - (percentage / 100) * circumference;
            circle.style.strokeDashoffset = offset;
        }, 100);
    }

    updateRiskAssessment(riskLevel) {
        const badge = document.getElementById('risk-badge');
        const riskClasses = {
            'Low': 'bg-green-100 text-green-800',
            'Medium': 'bg-yellow-100 text-yellow-800',
            'High': 'bg-red-100 text-red-800',
            'Critical': 'bg-red-200 text-red-900'
        };
        
        badge.className = `px-3 py-1 rounded-full text-sm font-medium ${riskClasses[riskLevel]}`;
        badge.innerHTML = `<i class="fas fa-shield-alt mr-1"></i>${riskLevel} Risk`;
        
        // Update risk counts (mock data for now)
        document.getElementById('low-risk-count').textContent = riskLevel === 'Low' ? this.data.summary.totalTests - 2 : 0;
        document.getElementById('medium-risk-count').textContent = riskLevel === 'Medium' ? 2 : 0;
        document.getElementById('high-risk-count').textContent = riskLevel === 'High' ? 2 : 0;
    }

    renderTestSuites() {
        const container = document.getElementById('test-suites-container');
        container.innerHTML = '';
        
        this.data.suites.forEach((suite, index) => {
            const suiteCard = this.createTestSuiteCard(suite, index);
            container.appendChild(suiteCard);
        });
    }

    createTestSuiteCard(suite, index) {
        const card = document.createElement('div');
        card.className = 'test-suite-card bg-white rounded-xl p-6 shadow-lg animate-fade-in-up';
        card.style.animationDelay = `${index * 0.1}s`;
        
        const passRateColor = suite.metrics.passRate >= 95 ? 'text-green-600' : 
                             suite.metrics.passRate >= 80 ? 'text-yellow-600' : 'text-red-600';
        
        card.innerHTML = `
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <div class="text-3xl mr-3">${suite.icon}</div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900">${suite.name}</h3>
                        <p class="text-sm text-gray-600">${suite.description}</p>
                    </div>
                </div>
                <div class="text-right">
                    <div class="text-2xl font-bold ${passRateColor}">${suite.metrics.passRate}%</div>
                    <div class="text-sm text-gray-500">Pass Rate</div>
                </div>
            </div>
            
            <div class="grid grid-cols-3 gap-4 mb-4">
                <div class="text-center">
                    <div class="text-lg font-semibold text-gray-900">${suite.metrics.totalTests}</div>
                    <div class="text-xs text-gray-500">Total</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-semibold text-green-600">${suite.metrics.passedTests}</div>
                    <div class="text-xs text-gray-500">Passed</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-semibold text-red-600">${suite.metrics.failedTests}</div>
                    <div class="text-xs text-gray-500">Failed</div>
                </div>
            </div>
            
            <div class="w-full bg-gray-200 rounded-full h-2">
                <div class="h-2 rounded-full transition-all duration-1000" 
                     style="width: ${suite.metrics.passRate}%; background-color: ${suite.color}"></div>
            </div>
            
            <div class="mt-4 flex justify-between text-sm text-gray-600">
                <span>Avg Time: ${Math.round(suite.metrics.avgExecutionTime / 1000)}s</span>
                <span>Improvements: +${suite.metrics.improvementCount}</span>
            </div>
        `;
        
        return card;
    }

    renderInsights() {
        // Render key findings
        const findingsList = document.getElementById('key-findings');
        findingsList.innerHTML = '';
        this.data.insights.keyFindings.forEach(finding => {
            const li = document.createElement('li');
            li.className = 'flex items-start';
            li.innerHTML = `
                <i class="fas fa-check-circle text-green-300 mr-2 mt-1"></i>
                <span class="text-blue-100">${finding}</span>
            `;
            findingsList.appendChild(li);
        });
        
        // Render recommendations
        const recommendationsList = document.getElementById('recommendations');
        recommendationsList.innerHTML = '';
        this.data.recommendations.forEach(rec => {
            const li = document.createElement('li');
            li.className = 'flex items-start';
            const priorityColor = rec.priority === 'High' ? 'text-yellow-300' : 'text-blue-300';
            li.innerHTML = `
                <i class="fas fa-arrow-right text-purple-300 mr-2 mt-1"></i>
                <div>
                    <span class="text-white font-medium">${rec.title}</span>
                    <span class="ml-2 px-2 py-1 bg-white bg-opacity-20 rounded text-xs ${priorityColor}">${rec.priority}</span>
                    <p class="text-blue-100 text-sm mt-1">${rec.description}</p>
                </div>
            `;
            recommendationsList.appendChild(li);
        });
        
        // Update confidence score
        document.getElementById('confidence-score').textContent = `${this.data.insights.confidenceScore}%`;
    }

    renderMetadata() {
        const generatedTime = new Date(this.data.metadata.generatedAt).toLocaleString();
        document.getElementById('generated-time').textContent = generatedTime;
        document.getElementById('report-id').textContent = this.data.metadata.reportId;
    }

    createCharts() {
        this.createPerformanceChart();
    }

    createPerformanceChart() {
        const ctx = document.getElementById('performance-chart').getContext('2d');
        
        const chartData = {
            labels: this.data.suites.map(suite => suite.name.replace(' Tests', '')),
            datasets: [
                {
                    label: 'Pass Rate (%)',
                    data: this.data.suites.map(suite => suite.metrics.passRate),
                    backgroundColor: this.data.suites.map(suite => suite.color + '40'),
                    borderColor: this.data.suites.map(suite => suite.color),
                    borderWidth: 2,
                    borderRadius: 8,
                    borderSkipped: false,
                }
            ]
        };
        
        this.charts.performance = new Chart(ctx, {
            type: 'bar',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: 'white',
                        bodyColor: 'white',
                        borderColor: 'rgba(255, 255, 255, 0.2)',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            afterLabel: function(context) {
                                const suite = chartData.labels[context.dataIndex];
                                const suiteData = this.data.suites[context.dataIndex];
                                return [
                                    `Total Tests: ${suiteData.metrics.totalTests}`,
                                    `Passed: ${suiteData.metrics.passedTests}`,
                                    `Failed: ${suiteData.metrics.failedTests}`
                                ];
                            }.bind(this)
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        },
                        ticks: {
                            callback: function(value) {
                                return value + '%';
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                },
                animation: {
                    duration: this.animationDuration,
                    easing: 'easeOutQuart'
                }
            }
        });
    }

    addAnimations() {
        // Add staggered animations to cards
        const cards = document.querySelectorAll('.animate-fade-in-up');
        cards.forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
        });
    }

    hideLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const dashboardContainer = document.getElementById('dashboard-container');
        
        setTimeout(() => {
            loadingScreen.style.opacity = '0';
            setTimeout(() => {
                loadingScreen.style.display = 'none';
                dashboardContainer.classList.remove('hidden');
                dashboardContainer.style.opacity = '0';
                setTimeout(() => {
                    dashboardContainer.style.opacity = '1';
                    dashboardContainer.style.transition = 'opacity 0.5s ease-in-out';
                }, 50);
            }, 500);
        }, 1500); // Show loading for at least 1.5 seconds for effect
    }

    showError(message) {
        const loadingScreen = document.getElementById('loading-screen');
        loadingScreen.innerHTML = `
            <div class="text-center text-white">
                <i class="fas fa-exclamation-triangle text-6xl text-red-400 mb-4"></i>
                <h2 class="text-2xl font-bold mb-2">Error Loading Dashboard</h2>
                <p class="text-red-200">${message}</p>
                <button onclick="location.reload()" class="mt-4 px-6 py-2 bg-white text-blue-600 rounded-lg hover:bg-gray-100 transition-colors">
                    Retry
                </button>
            </div>
        `;
    }

    startRealTimeUpdates() {
        // Simulate real-time updates (in a real implementation, this would connect to a WebSocket or polling mechanism)
        setInterval(() => {
            this.updateTimestamp();
        }, 30000); // Update every 30 seconds
    }

    updateTimestamp() {
        const now = new Date().toLocaleString();
        document.getElementById('generated-time').textContent = now;
    }
}

// Global functions
function initializeDashboard() {
    window.dashboard = new BiNDupDashboard();
}

function exportReport() {
    // Implementation for exporting the report
    console.log('📊 Exporting report...');
    
    // Create a comprehensive report object
    const reportData = {
        ...window.dashboard.data,
        exportedAt: new Date().toISOString(),
        exportFormat: 'JSON'
    };
    
    // Download as JSON
    const dataStr = JSON.stringify(reportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `bindup-test-report-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
}

// Add export functionality to button
document.addEventListener('DOMContentLoaded', function() {
    const exportButton = document.querySelector('button:has(.fa-download)');
    if (exportButton) {
        exportButton.addEventListener('click', exportReport);
    }
});

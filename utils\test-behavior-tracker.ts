/**
 * Test Behavior Tracker
 * Integrates with existing tests to track behaviors and detect regressions
 */

import { BugDetectionAlgorithm, TestResult, BehaviorCheck } from './bug-detection-algorithm';
// Use console.log for now since TestLogger import path varies
// const { TestLogger } = require('./test-logger');

export class TestBehaviorTracker {
  private bugDetector: BugDetectionAlgorithm;
  private currentTestResult: TestResult | null = null;
  private behaviorChecks: BehaviorCheck[] = [];

  constructor() {
    this.bugDetector = new BugDetectionAlgorithm();
    console.log('🔍 Test Behavior Tracker initialized');
  }

  /**
   * Start tracking a test
   */
  startTest(testName: string, buildVersion?: string): void {
    this.currentTestResult = {
      testName,
      status: 'failed', // Default to failed, will be updated on success
      duration: 0,
      errors: [],
      expectedBehaviors: [],
      actualBehaviors: [],
      timestamp: new Date().toISOString(),
      buildVersion
    };
    this.behaviorChecks = [];
    console.log(`🎯 Started tracking test: ${testName}`);
  }

  /**
   * Record a behavior check
   */
  recordBehavior(name: string, expected: any, actual: any, critical: boolean = false): void {
    if (!this.currentTestResult) {
      console.log('⚠️ No active test to record behavior');
      return;
    }

    const status = JSON.stringify(expected) === JSON.stringify(actual) ? 'pass' : 'fail';
    
    const behaviorCheck: BehaviorCheck = {
      name,
      expected,
      actual,
      status,
      critical
    };

    this.behaviorChecks.push(behaviorCheck);
    this.currentTestResult.actualBehaviors.push(behaviorCheck);

    console.log(`📊 Behavior recorded: ${name} - ${status.toUpperCase()}`);

    if (status === 'fail' && critical) {
      console.log(`🚨 CRITICAL BEHAVIOR FAILURE: ${name}`);
    }
  }

  /**
   * Record an error
   */
  recordError(error: string): void {
    if (!this.currentTestResult) return;
    
    this.currentTestResult.errors.push(error);
    console.log(`❌ Error recorded: ${error}`);
  }

  /**
   * Complete test tracking and analyze results
   */
  completeTest(status: 'passed' | 'failed' | 'skipped', duration: number): void {
    if (!this.currentTestResult) {
      console.log('⚠️ No active test to complete');
      return;
    }

    this.currentTestResult.status = status;
    this.currentTestResult.duration = duration;

    // Analyze against baseline
    const comparison = this.bugDetector.analyzeTestResult(this.currentTestResult);
    
    // Generate report if regressions detected
    if (comparison.regressions.length > 0) {
      const report = this.bugDetector.generateBugReport(comparison);
      console.log('🛡️ Bug Detection Report Generated');
      console.log(report);
    }

    // Record as baseline if test passed
    if (status === 'passed') {
      this.bugDetector.recordBaseline(this.currentTestResult);
      console.log(`✅ Test completed successfully: ${this.currentTestResult.testName}`);
    } else {
      console.log(`❌ Test failed: ${this.currentTestResult.testName}`);
    }

    this.currentTestResult = null;
    this.behaviorChecks = [];
  }

  /**
   * Helper methods for common behavior checks
   */

  /**
   * Check page navigation behavior
   */
  checkPageNavigation(expectedUrl: string, actualUrl: string): void {
    this.recordBehavior('Page Navigation Success', expectedUrl, actualUrl, true);
  }

  /**
   * Check element interaction behavior
   */
  checkElementInteraction(elementName: string, expectedResult: boolean, actualResult: boolean): void {
    this.recordBehavior(`${elementName} Interaction Success`, expectedResult, actualResult, true);
  }

  /**
   * Check data operation behavior
   */
  checkDataOperation(operationType: string, expectedResult: any, actualResult: any): void {
    this.recordBehavior(`${operationType} Success`, expectedResult, actualResult, true);
  }

  /**
   * Check UI state behavior
   */
  checkUIState(stateName: string, expectedState: any, actualState: any): void {
    this.recordBehavior(`UI State: ${stateName}`, expectedState, actualState, false);
  }

  /**
   * Check performance behavior
   */
  checkPerformance(operationName: string, expectedMaxDuration: number, actualDuration: number): void {
    const withinThreshold = actualDuration <= expectedMaxDuration;
    this.recordBehavior(
      `Performance: ${operationName}`,
      `<= ${expectedMaxDuration}ms`,
      `${actualDuration}ms`,
      false
    );
  }

  /**
   * Check loading state behavior
   */
  checkLoadingState(expectedLoaded: boolean, actualLoaded: boolean): void {
    this.recordBehavior('Loading State Handling', expectedLoaded, actualLoaded, true);
  }

  /**
   * Check popup handling behavior
   */
  checkPopupHandling(popupType: string, expectedHandled: boolean, actualHandled: boolean): void {
    this.recordBehavior(`Popup Handling: ${popupType}`, expectedHandled, actualHandled, true);
  }

  /**
   * Check form interaction behavior
   */
  checkFormInteraction(formName: string, expectedSuccess: boolean, actualSuccess: boolean): void {
    this.recordBehavior(`Form Interaction: ${formName}`, expectedSuccess, actualSuccess, true);
  }

  /**
   * Check error handling behavior
   */
  checkErrorHandling(errorType: string, expectedHandled: boolean, actualHandled: boolean): void {
    this.recordBehavior(`Error Handling: ${errorType}`, expectedHandled, actualHandled, false);
  }
}

/**
 * Global behavior tracker instance
 */
export const behaviorTracker = new TestBehaviorTracker();

/**
 * Decorator for automatic test tracking
 */
export function trackTestBehavior(testName: string, buildVersion?: string) {
  return function (target: any, propertyName: string, descriptor: PropertyDescriptor) {
    const method = descriptor.value;

    descriptor.value = async function (...args: any[]) {
      const startTime = Date.now();
      behaviorTracker.startTest(testName, buildVersion);

      try {
        const result = await method.apply(this, args);
        const duration = Date.now() - startTime;
        behaviorTracker.completeTest('passed', duration);
        return result;
      } catch (error) {
        const duration = Date.now() - startTime;
        behaviorTracker.recordError(error instanceof Error ? error.message : String(error));
        behaviorTracker.completeTest('failed', duration);
        throw error;
      }
    };

    return descriptor;
  };
}

// Export for CommonJS
module.exports = { TestBehaviorTracker, behaviorTracker, trackTestBehavior };

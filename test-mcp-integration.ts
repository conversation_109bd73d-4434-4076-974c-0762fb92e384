/**
 * Simple test to verify MCP integration works
 */

import { mcpClient } from './utils/mcp-client';

async function testMCPIntegration() {
  console.log('🚀 Testing MCP Integration...\n');

  try {
    // Test 1: Connect to MCP server
    console.log('📋 Test 1: MCP Server Connection');
    await mcpClient.connect();
    console.log('✅ MCP Server connected successfully\n');

    // Test 2: Smart wait functionality
    console.log('📋 Test 2: Smart Wait');
    const waitResult = await mcpClient.smartWait(
      'test-operation',
      'test-context',
      'intelligent',
      5000
    );
    console.log(`✅ Smart wait result: ${JSON.stringify(waitResult, null, 2)}\n`);

    // Test 3: Selector analysis
    console.log('📋 Test 3: Selector Analysis');
    const analysisResult = await mcpClient.analyzeSelectors('basic');
    console.log(`✅ Selector analysis result: ${JSON.stringify(analysisResult.summary, null, 2)}\n`);

    // Test 4: BiNDup optimization
    console.log('📋 Test 4: BiNDup Optimization');
    const optimizeResult = await mcpClient.optimizeBiNDup('launch', 'balanced');
    console.log(`✅ BiNDup optimization result: ${JSON.stringify(optimizeResult, null, 2)}\n`);

    console.log('🎉 All MCP integration tests passed!');

  } catch (error) {
    console.error('❌ MCP integration test failed:', error);
    process.exit(1);
  } finally {
    // Cleanup
    try {
      await mcpClient.disconnect();
      console.log('🔌 MCP Server disconnected');
    } catch (error) {
      console.error('⚠️ Error during cleanup:', error);
    }
  }
}

testMCPIntegration().catch(console.error);

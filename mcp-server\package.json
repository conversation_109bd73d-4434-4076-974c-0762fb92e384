{"name": "playwright-mcp-server", "version": "1.0.0", "description": "Playwright MCP Server for intelligent test automation with BiNDup optimization", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "tsx src/index.ts", "test": "jest", "lint": "eslint src/**/*.ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["playwright", "mcp", "automation", "testing", "bindui", "selector-analysis", "smart-loading"], "author": "Automation Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.5.0", "playwright": "^1.40.0", "zod": "^3.22.0", "winston": "^3.11.0", "lodash": "^4.17.21", "cheerio": "^1.0.0-rc.12", "sharp": "^0.33.0"}, "devDependencies": {"@types/node": "^20.0.0", "@types/lodash": "^4.14.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.0.0", "rimraf": "^5.0.0", "tsx": "^4.0.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}
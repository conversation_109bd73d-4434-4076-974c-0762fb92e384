<<<<<<< HEAD
# 🚫 BiNDup Image Automation Framework - Git Ignore File

# 📊 Test Results and Reports
test-results/
playwright-report/
results.json
results.xml

# 📸 Debug Screenshots (auto-generated during tests)
given-*.png
when-*.png
then-*.png
and-*.png
*-screenshot-*.png
screenshot-*.png

# 🎥 Test Videos and Traces
*.webm
trace.zip
*.trace

# 📝 Logs
*.log
logs/
debug.log
error.log

# 🔧 IDE and Editor Files
.vscode/
.idea/
*.swp
*.swo
*~

# 📦 Dependencies
=======
# Debug screenshots and test artifacts
debug-*.png
debug-*.jpg
debug-*.jpeg

# Removed Electron app - focusing on robust Playwright automation only
test-runner-app/

# Dependencies
>>>>>>> de463f33bcacc48b817cd1353c92354644444531
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

<<<<<<< HEAD
# 🏗️ Build Output
dist/
build/
out/

# 🔐 Environment and Secrets
=======
# Production builds
build/
dist/

# Environment variables
>>>>>>> de463f33bcacc48b817cd1353c92354644444531
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

<<<<<<< HEAD
# 💾 OS Generated Files
=======
# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS generated files
>>>>>>> de463f33bcacc48b817cd1353c92354644444531
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

<<<<<<< HEAD
# 🧪 Coverage Reports
coverage/
.nyc_output/

# 📱 Mobile
*.mobileprovision
*.p12

# 🎯 Temporary Files
tmp/
temp/
*.tmp
*.temp

# 🔄 Cache
.cache/
.parcel-cache/

# 📋 Keep Important Files (Negation Rules)
!test-data/
!test-data/images/
!test-data/images/testimage.jpg
!README.md
!playwright.config.ts
!tsconfig.json
=======
# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# next.js build output
.next

# nuxt.js build output
.nuxt

# vuepress build output
.vuepress/dist

# Serverless directories
.serverless

# FuseBox cache
.fusebox/

# DynamoDB Local files
.dynamodb/

# TernJS port file
.tern-port

# Test results
test-results/
playwright-report/
>>>>>>> de463f33bcacc48b817cd1353c92354644444531

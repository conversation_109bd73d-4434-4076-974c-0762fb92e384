#!/bin/bash

# 🔍 GitHub Actions Workflow Validation Script
# Validates the BiNDup automation workflow for syntax and best practices

echo "🔍 Validating BiNDup Automation GitHub Actions Workflow..."
echo ""

WORKFLOW_FILE=".github/workflows/bindup-automation-tests.yml"
VALIDATION_PASSED=true

# Check if workflow file exists
if [ ! -f "$WORKFLOW_FILE" ]; then
    echo "❌ Workflow file not found: $WORKFLOW_FILE"
    exit 1
fi

echo "✅ Workflow file found: $WORKFLOW_FILE"

# Basic YAML syntax validation (if yq is available)
if command -v yq &> /dev/null; then
    echo "🔍 Checking YAML syntax..."
    if yq eval '.' "$WORKFLOW_FILE" > /dev/null 2>&1; then
        echo "✅ YAML syntax is valid"
    else
        echo "❌ YAML syntax error detected"
        VALIDATION_PASSED=false
    fi
else
    echo "⚠️ yq not available, skipping YAML syntax check"
fi

# Check for required workflow components
echo ""
echo "🔍 Checking workflow components..."

# Check for required jobs
REQUIRED_JOBS=("detect-environment" "test-execution" "generate-report")
for job in "${REQUIRED_JOBS[@]}"; do
    if grep -q "^  $job:" "$WORKFLOW_FILE"; then
        echo "✅ Required job found: $job"
    else
        echo "❌ Missing required job: $job"
        VALIDATION_PASSED=false
    fi
done

# Check for trigger conditions
echo ""
echo "🔍 Checking trigger conditions..."

REQUIRED_TRIGGERS=("push" "pull_request" "schedule" "workflow_dispatch")
for trigger in "${REQUIRED_TRIGGERS[@]}"; do
    if grep -q "^  $trigger:" "$WORKFLOW_FILE"; then
        echo "✅ Trigger found: $trigger"
    else
        echo "❌ Missing trigger: $trigger"
        VALIDATION_PASSED=false
    fi
done

# Check for environment variables
echo ""
echo "🔍 Checking environment configuration..."

if grep -q "NODE_VERSION:" "$WORKFLOW_FILE"; then
    echo "✅ Node.js version configured"
else
    echo "❌ Node.js version not configured"
    VALIDATION_PASSED=false
fi

if grep -q "PLAYWRIGHT_BROWSERS_PATH:" "$WORKFLOW_FILE"; then
    echo "✅ Playwright browsers path configured"
else
    echo "❌ Playwright browsers path not configured"
    VALIDATION_PASSED=false
fi

# Check for matrix strategy
echo ""
echo "🔍 Checking matrix strategy..."

if grep -q "strategy:" "$WORKFLOW_FILE" && grep -q "matrix:" "$WORKFLOW_FILE"; then
    echo "✅ Matrix strategy configured"
else
    echo "❌ Matrix strategy not properly configured"
    VALIDATION_PASSED=false
fi

# Check for artifact handling
echo ""
echo "🔍 Checking artifact configuration..."

if grep -q "upload-artifact@v4" "$WORKFLOW_FILE"; then
    echo "✅ Artifact upload configured"
else
    echo "❌ Artifact upload not configured"
    VALIDATION_PASSED=false
fi

if grep -q "download-artifact@v4" "$WORKFLOW_FILE"; then
    echo "✅ Artifact download configured"
else
    echo "❌ Artifact download not configured"
    VALIDATION_PASSED=false
fi

# Check for notification setup
echo ""
echo "🔍 Checking notification configuration..."

if grep -q "action-slack" "$WORKFLOW_FILE"; then
    echo "✅ Slack notifications configured"
else
    echo "⚠️ Slack notifications not configured (optional)"
fi

if grep -q "action-send-mail" "$WORKFLOW_FILE"; then
    echo "✅ Email notifications configured"
else
    echo "⚠️ Email notifications not configured (optional)"
fi

# Check for security best practices
echo ""
echo "🔍 Checking security best practices..."

if grep -q "secrets\." "$WORKFLOW_FILE"; then
    echo "✅ GitHub Secrets usage detected"
else
    echo "⚠️ No GitHub Secrets usage detected"
fi

if grep -q "permissions:" "$WORKFLOW_FILE"; then
    echo "✅ Permissions configured"
else
    echo "⚠️ Permissions not explicitly configured"
fi

# Final validation result
echo ""
echo "=================================================="
if [ "$VALIDATION_PASSED" = true ]; then
    echo "🎉 Workflow validation PASSED!"
    echo "✅ The workflow is ready for production use"
    echo ""
    echo "📋 Next steps:"
    echo "1. Configure required GitHub Secrets"
    echo "2. Test the workflow with a manual dispatch"
    echo "3. Monitor the first few executions"
    echo "4. Set up notification channels (Slack/Email)"
    exit 0
else
    echo "❌ Workflow validation FAILED!"
    echo "🔧 Please fix the issues above before using the workflow"
    exit 1
fi

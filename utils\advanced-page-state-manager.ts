const { Page, BrowserContext } = require('@playwright/test');
const { TestLogger } = require('./test-logger');

/**
 * Advanced Page State Manager
 * Provides robust page state detection and automatic recovery mechanisms
 * for handling page closure scenarios in BiNDup automation
 */
export class AdvancedPageStateManager {
  private static instances: Map<string, AdvancedPageStateManager> = new Map();
  private page: Page;
  private context: BrowserContext;
  private pageId: string;
  private lastKnownUrl: string = '';
  private isMonitoring: boolean = false;
  private recoveryCallbacks: Array<() => Promise<void>> = [];
  private stateCheckInterval: NodeJS.Timeout | null = null;

  constructor(page: Page, context: BrowserContext, pageId: string) {
    this.page = page;
    this.context = context;
    this.pageId = pageId;
    this.lastKnownUrl = page.url();
    
    // Register this instance
    AdvancedPageStateManager.instances.set(pageId, this);
    
    TestLogger.logStep(`🔧 Advanced Page State Manager initialized for: ${pageId}`, 'success');
  }

  /**
   * Start monitoring page state with automatic recovery
   */
  async startMonitoring(): Promise<void> {
    if (this.isMonitoring) {
      TestLogger.logStep('Page state monitoring already active', 'warning');
      return;
    }

    this.isMonitoring = true;
    TestLogger.logStep('🔍 Starting advanced page state monitoring', 'start');

    // Set up page event listeners
    this.page.on('close', () => {
      TestLogger.logStep('⚠️ Page closure detected by event listener', 'warning');
      this.handlePageClosure();
    });

    this.page.on('crash', () => {
      TestLogger.logStep('💥 Page crash detected', 'error');
      this.handlePageCrash();
    });

    // Start periodic state checking
    this.stateCheckInterval = setInterval(async () => {
      await this.performStateCheck();
    }, 5000); // Check every 5 seconds

    TestLogger.logStep('✅ Advanced page state monitoring started', 'success');
  }

  /**
   * Stop monitoring page state
   */
  stopMonitoring(): void {
    this.isMonitoring = false;
    
    if (this.stateCheckInterval) {
      clearInterval(this.stateCheckInterval);
      this.stateCheckInterval = null;
    }

    TestLogger.logStep('🛑 Page state monitoring stopped', 'success');
  }

  /**
   * Check if page is in a stable state for operations
   */
  async isPageStable(): Promise<boolean> {
    try {
      // Check if page is closed
      if (this.page.isClosed()) {
        TestLogger.logStep('❌ Page is closed', 'warning');
        return false;
      }

      // Check if page is responsive
      const isResponsive = await this.page.evaluate(() => {
        return document.readyState === 'complete' && 
               typeof document.addEventListener === 'function';
      });

      if (!isResponsive) {
        TestLogger.logStep('❌ Page is not responsive', 'warning');
        return false;
      }

      // Check for loading indicators
      const hasLoadingIndicators = await this.checkForLoadingIndicators();
      if (hasLoadingIndicators) {
        TestLogger.logStep('⏳ Page has active loading indicators', 'warning');
        return false;
      }

      // Update last known URL
      this.lastKnownUrl = this.page.url();
      return true;

    } catch (error) {
      TestLogger.logStep(`❌ Page stability check failed: ${error}`, 'error');
      return false;
    }
  }

  /**
   * Wait for page to become stable with timeout
   */
  async waitForStableState(timeoutMs: number = 30000): Promise<boolean> {
    TestLogger.logStep('⏳ Waiting for page to become stable', 'start');
    
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeoutMs) {
      if (await this.isPageStable()) {
        TestLogger.logStep('✅ Page is now stable', 'success');
        return true;
      }
      
      await this.page.waitForTimeout(1000);
    }

    TestLogger.logStep('⚠️ Page stability timeout reached', 'warning');
    return false;
  }

  /**
   * Attempt to recover from page closure
   */
  async attemptRecovery(): Promise<boolean> {
    TestLogger.logStep('🔄 Attempting page recovery', 'start');

    try {
      // If page is closed, try to create a new one
      if (this.page.isClosed()) {
        TestLogger.logStep('🔄 Creating new page for recovery', 'start');
        
        const newPage = await this.context.newPage();
        
        // Navigate to last known URL
        if (this.lastKnownUrl) {
          await newPage.goto(this.lastKnownUrl, {
            waitUntil: 'domcontentloaded',
            timeout: 30000
          });
          TestLogger.logStep(`🔄 Navigated to last known URL: ${this.lastKnownUrl}`, 'success');
        }

        // Update page reference
        this.page = newPage;
        
        // Execute recovery callbacks
        for (const callback of this.recoveryCallbacks) {
          try {
            await callback();
            TestLogger.logStep('✅ Recovery callback executed successfully', 'success');
          } catch (error) {
            TestLogger.logStep(`⚠️ Recovery callback failed: ${error}`, 'warning');
          }
        }

        TestLogger.logStep('✅ Page recovery completed', 'success');
        return true;
      }

      return true;

    } catch (error) {
      TestLogger.logStep(`❌ Page recovery failed: ${error}`, 'error');
      return false;
    }
  }

  /**
   * Register a recovery callback
   */
  addRecoveryCallback(callback: () => Promise<void>): void {
    this.recoveryCallbacks.push(callback);
    TestLogger.logStep('📝 Recovery callback registered', 'success');
  }

  /**
   * Get current page instance (may be recovered)
   */
  getCurrentPage(): Page {
    return this.page;
  }

  /**
   * Check for loading indicators that suggest page instability
   */
  private async checkForLoadingIndicators(): Promise<boolean> {
    try {
      const loadingSelectors = [
        '.loading',
        '.spinner',
        '.cs-loading',
        '.x-mask',
        'text=読み込み中',
        'text=処理中',
        '[data-loading="true"]'
      ];

      for (const selector of loadingSelectors) {
        try {
          const isVisible = await this.page.locator(selector).isVisible({ timeout: 1000 });
          if (isVisible) {
            return true;
          }
        } catch (error) {
          // Selector not found, continue
        }
      }

      return false;

    } catch (error) {
      TestLogger.logStep(`⚠️ Loading indicator check failed: ${error}`, 'warning');
      return false;
    }
  }

  /**
   * Perform periodic state check
   */
  private async performStateCheck(): Promise<void> {
    if (!this.isMonitoring) return;

    try {
      const isStable = await this.isPageStable();
      
      if (!isStable) {
        TestLogger.logStep('⚠️ Page instability detected during periodic check', 'warning');
        
        // Attempt recovery if page is closed
        if (this.page.isClosed()) {
          await this.attemptRecovery();
        }
      }

    } catch (error) {
      TestLogger.logStep(`⚠️ Periodic state check error: ${error}`, 'warning');
    }
  }

  /**
   * Handle page closure event
   */
  private handlePageClosure(): void {
    TestLogger.logStep('🚨 Handling page closure event', 'warning');
    
    // Trigger recovery in next tick to avoid event handler issues
    setTimeout(async () => {
      await this.attemptRecovery();
    }, 100);
  }

  /**
   * Handle page crash event
   */
  private handlePageCrash(): void {
    TestLogger.logStep('💥 Handling page crash event', 'error');
    
    // Trigger recovery in next tick
    setTimeout(async () => {
      await this.attemptRecovery();
    }, 100);
  }

  /**
   * Static method to get or create manager instance
   */
  static getManager(page: Page, context: BrowserContext, pageId: string): AdvancedPageStateManager {
    let manager = AdvancedPageStateManager.instances.get(pageId);
    
    if (!manager || manager.page.isClosed()) {
      manager = new AdvancedPageStateManager(page, context, pageId);
    }
    
    return manager;
  }

  /**
   * Static method to cleanup all managers
   */
  static cleanup(): void {
    for (const [pageId, manager] of AdvancedPageStateManager.instances) {
      manager.stopMonitoring();
    }
    AdvancedPageStateManager.instances.clear();
    TestLogger.logStep('🧹 All page state managers cleaned up', 'success');
  }
}

// Export for CommonJS
module.exports = { AdvancedPageStateManager };

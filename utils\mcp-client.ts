/**
 * MCP Client for Playwright automation tests
 * Provides intelligent automation capabilities through the Playwright MCP Server
 */

import { spawn, ChildProcess } from 'child_process';
import { join } from 'path';

export interface MCPResponse {
  result?: any;
  error?: any;
  id: number;
}

export class PlaywrightMCPClient {
  private serverProcess: ChildProcess | null = null;
  private requestId = 0;
  private pendingRequests = new Map<number, { resolve: Function; reject: Function }>();
  private isConnected = false;

  constructor() {
  }

  /**
   * Start the MCP server
   */
  async connect(): Promise<void> {
    if (this.isConnected) {
      return;
    }

    try {
      const serverPath = join(process.cwd(), 'mcp-server', 'dist', 'index.js');
      
      this.serverProcess = spawn('node', [serverPath], {
        stdio: ['pipe', 'pipe', 'pipe']
      });

      // Handle server output
      this.serverProcess.stdout?.on('data', (data) => {
        const lines = data.toString().trim().split('\n');
        
        for (const line of lines) {
          if (line.trim()) {
            try {
              const response = JSON.parse(line);
              this.handleResponse(response);
            } catch (error) {
              console.log('MCP Server output:', line);
            }
          }
        }
      });

      this.serverProcess.stderr?.on('data', (data) => {
        console.log('MCP Server log:', data.toString());
      });

      this.serverProcess.on('close', (code) => {
        console.log(`MCP Server exited with code ${code}`);
        this.isConnected = false;
      });

      // Wait a moment for server to start
      await new Promise(resolve => setTimeout(resolve, 1000));
      this.isConnected = true;
      
      console.log('✅ MCP Server connected successfully');

    } catch (error) {
      console.error('❌ Failed to connect to MCP Server:', error);
      throw error;
    }
  }

  /**
   * Disconnect from the MCP server
   */
  async disconnect(): Promise<void> {
    if (this.serverProcess) {
      this.serverProcess.kill();
      this.serverProcess = null;
    }
    this.isConnected = false;
    console.log('🔌 MCP Server disconnected');
  }

  /**
   * Invoke an MCP tool
   */
  async invoke(toolName: string, args: any = {}): Promise<any> {
    if (!this.isConnected) {
      await this.connect();
    }

    return new Promise((resolve, reject) => {
      const id = ++this.requestId;
      
      const message = {
        jsonrpc: '2.0',
        id,
        method: 'tools/call',
        params: {
          name: toolName,
          arguments: args
        }
      };

      this.pendingRequests.set(id, { resolve, reject });

      // Send message to server
      if (this.serverProcess?.stdin) {
        this.serverProcess.stdin.write(JSON.stringify(message) + '\n');
      } else {
        reject(new Error('MCP Server not connected'));
      }

      // Timeout after 30 seconds
      setTimeout(() => {
        if (this.pendingRequests.has(id)) {
          this.pendingRequests.delete(id);
          reject(new Error(`MCP request timeout for ${toolName}`));
        }
      }, 30000);
    });
  }

  /**
   * Handle response from MCP server
   */
  private handleResponse(response: MCPResponse): void {
    const { id } = response;
    const pending = this.pendingRequests.get(id);
    
    if (pending) {
      this.pendingRequests.delete(id);
      
      if (response.error) {
        pending.reject(new Error(response.error.message || 'MCP request failed'));
      } else {
        // Parse the result if it's a JSON string
        let result = response.result;
        if (result?.content?.[0]?.text) {
          try {
            result = JSON.parse(result.content[0].text);
          } catch (error) {
            // Keep original result if parsing fails
          }
        }
        pending.resolve(result);
      }
    }
  }

  /**
   * Smart wait with intelligent loading strategies
   */
  async smartWait(operation: string, context: string, strategy: 'adaptive' | 'race' | 'sequential' | 'intelligent' = 'intelligent', maxWait: number = 30000): Promise<any> {
    console.log(`🔄 Smart wait: ${operation} (${strategy})`);
    
    const result = await this.invoke('playwright-smart-wait', {
      operation,
      context,
      strategy,
      maxWait
    });

    if (result.success) {
      console.log(`✅ Smart wait completed in ${result.duration}ms`);
    } else {
      console.log(`⚠️ Smart wait failed after ${result.duration}ms`);
    }

    return result;
  }

  /**
   * Analyze selectors for stability and alternatives
   */
  async analyzeSelectors(analysisDepth: 'basic' | 'deep' | 'comprehensive' = 'deep', targetElement?: string, context?: string): Promise<any> {
    console.log(`🔍 Analyzing selectors: ${analysisDepth} depth`);
    
    const result = await this.invoke('playwright-analyze-selectors', {
      analysisDepth,
      targetElement,
      context
    });

    console.log(`📊 Found ${result.recommendations?.length || 0} selector recommendations`);
    return result;
  }

  /**
   * BiNDup-specific optimizations
   */
  async optimizeBiNDup(operation: 'launch' | 'navigate' | 'edit' | 'save' | 'popup-handle', optimizationLevel: 'conservative' | 'balanced' | 'aggressive' = 'balanced', context?: string): Promise<any> {
    console.log(`🎯 BiNDup optimization: ${operation} (${optimizationLevel})`);
    
    const result = await this.invoke('playwright-bindui-optimize', {
      operation,
      optimizationLevel,
      context
    });

    console.log(`🚀 Applied ${result.optimizations?.length || 0} optimizations`);
    return result;
  }

  /**
   * Debug flow with comprehensive insights
   */
  async debugFlow(step: string, captureLevel: 'basic' | 'detailed' | 'comprehensive' = 'detailed', highlightElements?: string[]): Promise<any> {
    console.log(`🐛 Debug flow: ${step}`);
    
    const result = await this.invoke('playwright-debug-flow', {
      step,
      captureLevel,
      highlightElements,
      includeScreenshot: true
    });

    if (result.insights?.length > 0) {
      console.log(`💡 Insights: ${result.insights.join(', ')}`);
    }

    return result;
  }

  /**
   * Initialize browser through MCP
   */
  async initBrowser(headless: boolean = false, url?: string): Promise<any> {
    console.log(`🌐 Initializing browser: headless=${headless}`);
    
    const result = await this.invoke('playwright-init-browser', {
      headless,
      url
    });

    console.log(`✅ Browser initialized: ${result.message}`);
    return result;
  }

  /**
   * Close browser through MCP
   */
  async closeBrowser(): Promise<any> {
    console.log(`🔌 Closing browser`);
    
    const result = await this.invoke('playwright-close-browser', {});
    
    console.log(`✅ Browser closed: ${result.message}`);
    return result;
  }

  /**
   * Get best selector from analysis results
   */
  getBestSelector(analysisResult: any, fallback: string): string {
    if (analysisResult?.recommendations?.length > 0) {
      const best = analysisResult.recommendations[0];
      if (best.confidence > 70) {
        console.log(`📍 Using high-confidence selector: ${best.selector} (${best.confidence}%)`);
        return best.selector;
      }
    }

    console.log(`📍 Using fallback selector: ${fallback}`);
    return fallback;
  }

  /**
   * Get selector alternatives for fallback
   */
  getSelectorAlternatives(analysisResult: any): string[] {
    if (analysisResult?.recommendations) {
      return analysisResult.recommendations
        .filter((rec: any) => rec.confidence > 50)
        .map((rec: any) => rec.selector);
    }
    return [];
  }
}

// Global MCP client instance
export const mcpClient = new PlaywrightMCPClient();

const { Page, BrowserContext } = require('@playwright/test');
const { TestLogger } = require('./test-logger');
const { AdvancedPageStateManager } = require('./advanced-page-state-manager');
const { IframeStabilityFramework } = require('./iframe-stability-framework');

/**
 * Context Recovery System
 * Provides automatic context recovery that can restore test state
 * after page closure without losing progress in BiNDup automation
 */
export class ContextRecoverySystem {
  private page: Page;
  private context: BrowserContext;
  private pageStateManager: AdvancedPageStateManager;
  private iframeFramework: IframeStabilityFramework;
  private testState: TestState;
  private recoveryStrategies: Map<string, RecoveryStrategy> = new Map();

  constructor(page: Page, context: BrowserContext) {
    this.page = page;
    this.context = context;
    this.pageStateManager = AdvancedPageStateManager.getManager(page, context, 'main');
    this.iframeFramework = new IframeStabilityFramework(page, this.pageStateManager);
    this.testState = new TestState();
    
    this.initializeRecoveryStrategies();
    TestLogger.logStep('🔄 Context Recovery System initialized', 'success');
  }

  /**
   * Save current test state checkpoint
   */
  async saveCheckpoint(checkpointName: string, state: any): Promise<void> {
    TestLogger.logStep(`💾 Saving checkpoint: ${checkpointName}`, 'start');
    
    try {
      const checkpoint: Checkpoint = {
        name: checkpointName,
        timestamp: Date.now(),
        url: this.page.url(),
        state: state,
        pageTitle: await this.page.title(),
        cookies: await this.context.cookies(),
        localStorage: await this.page.evaluate(() => {
          const storage: any = {};
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key) storage[key] = localStorage.getItem(key);
          }
          return storage;
        })
      };

      this.testState.addCheckpoint(checkpoint);
      TestLogger.logStep(`✅ Checkpoint saved: ${checkpointName}`, 'success');

    } catch (error) {
      TestLogger.logStep(`⚠️ Failed to save checkpoint: ${error}`, 'warning');
    }
  }

  /**
   * Restore from the latest checkpoint
   */
  async restoreFromLatestCheckpoint(): Promise<boolean> {
    const latestCheckpoint = this.testState.getLatestCheckpoint();
    
    if (!latestCheckpoint) {
      TestLogger.logStep('⚠️ No checkpoint available for restoration', 'warning');
      return false;
    }

    return await this.restoreFromCheckpoint(latestCheckpoint.name);
  }

  /**
   * Restore from a specific checkpoint
   */
  async restoreFromCheckpoint(checkpointName: string): Promise<boolean> {
    TestLogger.logStep(`🔄 Restoring from checkpoint: ${checkpointName}`, 'start');
    
    const checkpoint = this.testState.getCheckpoint(checkpointName);
    if (!checkpoint) {
      TestLogger.logStep(`❌ Checkpoint not found: ${checkpointName}`, 'error');
      return false;
    }

    try {
      // Restore page state
      await this.restorePageState(checkpoint);
      
      // Restore application state
      await this.restoreApplicationState(checkpoint);
      
      TestLogger.logStep(`✅ Successfully restored from checkpoint: ${checkpointName}`, 'success');
      return true;

    } catch (error) {
      TestLogger.logStep(`❌ Failed to restore from checkpoint: ${error}`, 'error');
      return false;
    }
  }

  /**
   * Execute operation with automatic recovery
   */
  async executeWithRecovery<T>(
    operation: () => Promise<T>,
    operationName: string,
    checkpointName?: string
  ): Promise<T> {
    TestLogger.logStep(`🛡️ Executing ${operationName} with recovery protection`, 'start');

    // Save checkpoint before operation
    if (checkpointName) {
      await this.saveCheckpoint(checkpointName, { operation: operationName });
    }

    try {
      const result = await operation();
      TestLogger.logStep(`✅ ${operationName} completed successfully`, 'success');
      return result;

    } catch (error) {
      TestLogger.logStep(`⚠️ ${operationName} failed, attempting recovery: ${error}`, 'warning');
      
      // Attempt recovery
      const recoverySuccess = await this.attemptRecovery(operationName);
      
      if (recoverySuccess && checkpointName) {
        // Retry operation after recovery
        TestLogger.logStep(`🔄 Retrying ${operationName} after recovery`, 'start');
        try {
          const result = await operation();
          TestLogger.logStep(`✅ ${operationName} succeeded after recovery`, 'success');
          return result;
        } catch (retryError) {
          TestLogger.logStep(`❌ ${operationName} failed even after recovery: ${retryError}`, 'error');
          throw retryError;
        }
      }

      throw error;
    }
  }

  /**
   * Attempt automatic recovery based on failure type
   */
  async attemptRecovery(operationName: string): Promise<boolean> {
    TestLogger.logStep(`🔄 Attempting automatic recovery for: ${operationName}`, 'start');

    // Check if page is closed
    if (this.page.isClosed()) {
      TestLogger.logStep('🔄 Page closure detected, attempting page recovery', 'start');
      return await this.recoverFromPageClosure();
    }

    // Check if iframe is inaccessible
    const iframe = await this.iframeFramework.getStableIframe();
    if (!iframe) {
      TestLogger.logStep('🔄 Iframe inaccessible, attempting iframe recovery', 'start');
      return await this.recoverFromIframeFailure();
    }

    // Check for specific recovery strategies
    const strategy = this.recoveryStrategies.get(operationName);
    if (strategy) {
      TestLogger.logStep(`🔄 Using specific recovery strategy for: ${operationName}`, 'start');
      return await strategy.execute();
    }

    // Generic recovery
    return await this.performGenericRecovery();
  }

  /**
   * Recover from page closure
   */
  private async recoverFromPageClosure(): Promise<boolean> {
    TestLogger.logStep('🔄 Recovering from page closure', 'start');

    try {
      // Use page state manager recovery
      const recoverySuccess = await this.pageStateManager.attemptRecovery();
      
      if (recoverySuccess) {
        // Update page reference
        this.page = this.pageStateManager.getCurrentPage();
        
        // Restore from latest checkpoint
        await this.restoreFromLatestCheckpoint();
        
        TestLogger.logStep('✅ Page closure recovery successful', 'success');
        return true;
      }

      TestLogger.logStep('❌ Page closure recovery failed', 'error');
      return false;

    } catch (error) {
      TestLogger.logStep(`❌ Page closure recovery error: ${error}`, 'error');
      return false;
    }
  }

  /**
   * Recover from iframe failure
   */
  private async recoverFromIframeFailure(): Promise<boolean> {
    TestLogger.logStep('🔄 Recovering from iframe failure', 'start');

    try {
      // Use iframe framework recovery
      const iframe = await this.iframeFramework.getIframeWithFallback();
      
      if (iframe) {
        TestLogger.logStep('✅ Iframe recovery successful', 'success');
        return true;
      }

      TestLogger.logStep('❌ Iframe recovery failed', 'error');
      return false;

    } catch (error) {
      TestLogger.logStep(`❌ Iframe recovery error: ${error}`, 'error');
      return false;
    }
  }

  /**
   * Perform generic recovery
   */
  private async performGenericRecovery(): Promise<boolean> {
    TestLogger.logStep('🔄 Performing generic recovery', 'start');

    try {
      // Wait for page stability
      await this.pageStateManager.waitForStableState(15000);
      
      // Handle any popups that might have appeared
      await this.handleRecoveryPopups();
      
      // Wait for iframe stability
      await this.iframeFramework.waitForIframeStability(10000);
      
      TestLogger.logStep('✅ Generic recovery completed', 'success');
      return true;

    } catch (error) {
      TestLogger.logStep(`❌ Generic recovery failed: ${error}`, 'error');
      return false;
    }
  }

  /**
   * Handle popups that might appear during recovery
   */
  private async handleRecoveryPopups(): Promise<void> {
    const popupSelectors = [
      '#button-1014',
      '#button-1031',
      'button:has-text("閉じる")',
      'button:has-text("OK")',
      '.x-tool-close'
    ];

    for (const selector of popupSelectors) {
      try {
        const popup = this.page.locator(selector);
        if (await popup.isVisible({ timeout: 2000 })) {
          await popup.click();
          await this.page.waitForTimeout(1000);
          TestLogger.logStep(`🔔 Recovery popup handled: ${selector}`, 'success');
        }
      } catch (error) {
        // Continue with next selector
      }
    }
  }

  /**
   * Restore page state from checkpoint
   */
  private async restorePageState(checkpoint: Checkpoint): Promise<void> {
    TestLogger.logStep('🔄 Restoring page state', 'start');

    try {
      // Navigate to checkpoint URL if different
      if (this.page.url() !== checkpoint.url) {
        await this.page.goto(checkpoint.url, {
          waitUntil: 'domcontentloaded',
          timeout: 30000
        });
      }

      // Restore cookies
      await this.context.addCookies(checkpoint.cookies);

      // Restore localStorage
      await this.page.evaluate((storage) => {
        for (const [key, value] of Object.entries(storage)) {
          localStorage.setItem(key, value as string);
        }
      }, checkpoint.localStorage);

      TestLogger.logStep('✅ Page state restored', 'success');

    } catch (error) {
      TestLogger.logStep(`⚠️ Page state restoration failed: ${error}`, 'warning');
    }
  }

  /**
   * Restore application-specific state
   */
  private async restoreApplicationState(checkpoint: Checkpoint): Promise<void> {
    TestLogger.logStep('🔄 Restoring application state', 'start');

    try {
      // Handle BiNDup-specific state restoration
      await this.handleRecoveryPopups();
      
      // Wait for application to stabilize
      await this.page.waitForTimeout(3000);
      
      TestLogger.logStep('✅ Application state restored', 'success');

    } catch (error) {
      TestLogger.logStep(`⚠️ Application state restoration failed: ${error}`, 'warning');
    }
  }

  /**
   * Initialize recovery strategies for specific operations
   */
  private initializeRecoveryStrategies(): void {
    // Block addition recovery
    this.recoveryStrategies.set('blockAddition', {
      execute: async () => {
        TestLogger.logStep('🔄 Block addition recovery strategy', 'start');
        
        // Ensure we're in page edit mode
        try {
          const pageEditButton = this.page.locator('text=ページ編集');
          if (await pageEditButton.isVisible({ timeout: 5000 })) {
            await pageEditButton.click();
            await this.page.waitForTimeout(2000);
          }
        } catch (error) {
          // Already in edit mode or button not found
        }
        
        return true;
      }
    });

    // Page duplication recovery
    this.recoveryStrategies.set('pageDuplication', {
      execute: async () => {
        TestLogger.logStep('🔄 Page duplication recovery strategy', 'start');
        
        // Navigate back to site editor if needed
        const currentUrl = this.page.url();
        if (!currentUrl.includes('siteEditor')) {
          await this.page.goto(currentUrl.replace('siteTheater', 'siteEditor'), {
            waitUntil: 'domcontentloaded',
            timeout: 30000
          });
        }
        
        return true;
      }
    });
  }

  /**
   * Cleanup resources
   */
  cleanup(): void {
    this.pageStateManager.stopMonitoring();
    this.iframeFramework.cleanup();
    this.testState.clear();
    this.recoveryStrategies.clear();
    
    TestLogger.logStep('🧹 Context Recovery System cleaned up', 'success');
  }
}

/**
 * Test State Management
 */
class TestState {
  private checkpoints: Map<string, Checkpoint> = new Map();

  addCheckpoint(checkpoint: Checkpoint): void {
    this.checkpoints.set(checkpoint.name, checkpoint);
  }

  getCheckpoint(name: string): Checkpoint | undefined {
    return this.checkpoints.get(name);
  }

  getLatestCheckpoint(): Checkpoint | undefined {
    let latest: Checkpoint | undefined;
    
    for (const checkpoint of this.checkpoints.values()) {
      if (!latest || checkpoint.timestamp > latest.timestamp) {
        latest = checkpoint;
      }
    }
    
    return latest;
  }

  clear(): void {
    this.checkpoints.clear();
  }
}

/**
 * Interfaces
 */
interface Checkpoint {
  name: string;
  timestamp: number;
  url: string;
  state: any;
  pageTitle: string;
  cookies: any[];
  localStorage: any;
}

interface RecoveryStrategy {
  execute: () => Promise<boolean>;
}

// Export for CommonJS
module.exports = { ContextRecoverySystem };

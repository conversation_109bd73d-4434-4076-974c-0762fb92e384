import { Page, ElementHandle } from 'playwright';
import {
  SelectorAnalysis,
  SelectorRecommendation,
  ElementAttributes,
  SelectorAnalysisError
} from '../types/index.js';

export class SelectorAnalyzer {
  private selectorHistory: Map<string, SelectorAnalysis> = new Map();
  private stabilityThreshold = 0.8; // 80% success rate for stable classification

  constructor() {
  }

  /**
   * Analyze all selectors on a page for stability and alternatives
   */
  async analyzePageSelectors(
    page: Page, 
    analysisDepth: 'basic' | 'deep' | 'comprehensive' = 'basic'
  ): Promise<SelectorAnalysis[]> {
    try {
      console.log('Starting page selector analysis', { depth: analysisDepth });

      const elements = await this.getAllPageElements(page, analysisDepth);
      const analyses: SelectorAnalysis[] = [];

      for (const element of elements) {
        const analysis = await this.analyzeElement(page, element);
        analyses.push(analysis);
      }

      console.log('Completed page selector analysis', {
        totalElements: analyses.length,
        stableElements: analyses.filter(a => a.stability === 'stable').length
      });

      return analyses;

    } catch (error) {
      console.error('Failed to analyze page selectors', { error });
      throw new SelectorAnalysisError('Page selector analysis failed', { error });
    }
  }

  /**
   * Find the best selector for a specific element
   */
  async findBestSelector(
    page: Page, 
    targetElement: string | ElementHandle
  ): Promise<SelectorRecommendation> {
    try {
      let element: ElementHandle;
      
      if (typeof targetElement === 'string') {
        element = await page.locator(targetElement).elementHandle() || 
                 await page.waitForSelector(targetElement, { timeout: 5000 });
      } else {
        element = targetElement;
      }

      if (!element) {
        throw new SelectorAnalysisError('Target element not found');
      }

      const alternatives = await this.generateSelectorAlternatives(page, element);
      const bestSelector = await this.evaluateSelectorStability(page, alternatives);

      return bestSelector;

    } catch (error) {
      console.error('Failed to find best selector', { error, targetElement });
      throw new SelectorAnalysisError('Best selector analysis failed', { error });
    }
  }

  /**
   * Generate multiple selector alternatives for an element
   */
  private async generateSelectorAlternatives(
    page: Page, 
    element: ElementHandle
  ): Promise<string[]> {
    const alternatives: string[] = [];

    try {
      // Get element attributes
      const attributes = await this.getElementAttributes(element);
      
      // Strategy 1: ID-based (if stable)
      if (attributes.id && !this.isDynamicId(attributes.id)) {
        alternatives.push(`#${attributes.id}`);
      }

      // Strategy 2: Class-based
      if (attributes.className) {
        const classes = attributes.className.split(' ').filter(c => c.trim());
        for (const cls of classes) {
          if (!this.isDynamicClass(cls)) {
            alternatives.push(`.${cls}`);
          }
        }
        
        // Combination of stable classes
        const stableClasses = classes.filter(c => !this.isDynamicClass(c));
        if (stableClasses.length > 1) {
          alternatives.push(`.${stableClasses.join('.')}`);
        }
      }

      // Strategy 3: Data attributes
      for (const [key, value] of Object.entries(attributes.dataAttributes)) {
        if (!this.isDynamicValue(value)) {
          alternatives.push(`[data-${key}="${value}"]`);
          alternatives.push(`[${key}="${value}"]`);
        }
      }

      // Strategy 4: Text-based
      if (attributes.textContent && attributes.textContent.trim().length > 0) {
        const text = attributes.textContent.trim();
        if (text.length < 50 && !this.isDynamicText(text)) {
          alternatives.push(`text=${text}`);
          alternatives.push(`${attributes.tagName}:has-text("${text}")`);
          alternatives.push(`button:has-text("${text}")`);
        }
      }

      // Strategy 5: ARIA attributes
      if (attributes.ariaLabel) {
        alternatives.push(`[aria-label="${attributes.ariaLabel}"]`);
      }

      // Strategy 6: Tag + attribute combinations
      alternatives.push(
        `${attributes.tagName}[class*="${attributes.className?.split(' ')[0]}"]`
      );

      // Strategy 7: Position-based (last resort)
      alternatives.push(`${attributes.tagName}:nth-child(1)`);
      alternatives.push(`${attributes.tagName}:first-child`);

      // Strategy 8: BiNDup-specific patterns
      alternatives.push(...this.generateBiNDupSpecificSelectors(attributes));

      return [...new Set(alternatives)]; // Remove duplicates

    } catch (error) {
      console.error('Failed to generate selector alternatives', { error });
      return [];
    }
  }

  /**
   * Generate BiNDup-specific selector patterns
   */
  private generateBiNDupSpecificSelectors(attributes: ElementAttributes): string[] {
    const selectors: string[] = [];

    // BiNDup button patterns
    if (attributes.id?.startsWith('button-')) {
      selectors.push(`#${attributes.id}`);
      selectors.push(`button[id^="button-"]`);
    }

    // BiNDup popup close patterns
    if (attributes.textContent?.includes('閉じる')) {
      selectors.push('button:has-text("閉じる")');
      selectors.push('[role="button"]:has-text("閉じる")');
      selectors.push('.x-btn:has-text("閉じる")');
    }

    // BiNDup site item patterns
    if (attributes.className?.includes('cs-item')) {
      selectors.push('.cs-item[draggable="true"]');
      selectors.push('.cs-item .cs-select.cs-click');
    }

    // BiNDup block patterns
    if (attributes.className?.includes('b-plain')) {
      selectors.push('.b-plain.cssskin-_block_billboard');
      selectors.push('.b-plain.cssskin-_block_main');
      selectors.push('.b-plain.cssskin-_block_header');
    }

    // BiNDup iframe patterns
    if (attributes.tagName === 'iframe') {
      // Check dataAttributes for name
      if (attributes.dataAttributes?.name) {
        selectors.push(`iframe[name="${attributes.dataAttributes.name}"]`);
      }
    }

    return selectors;
  }

  /**
   * Evaluate selector stability and performance
   */
  private async evaluateSelectorStability(
    page: Page, 
    selectors: string[]
  ): Promise<SelectorRecommendation> {
    const evaluations: Array<{
      selector: string;
      confidence: number;
      reasoning: string;
      stability: 'high' | 'medium' | 'low';
    }> = [];

    for (const selector of selectors) {
      try {
        // Test selector reliability
        const startTime = Date.now();
        const elements = await page.locator(selector).all();
        const duration = Date.now() - startTime;

        let confidence = 0;
        let reasoning = '';
        let stability: 'high' | 'medium' | 'low' = 'low';

        // Scoring criteria
        if (elements.length === 1) {
          confidence += 40; // Unique element
          reasoning += 'Unique element match. ';
        } else if (elements.length === 0) {
          confidence = 0;
          reasoning = 'Element not found. ';
          continue;
        } else {
          confidence += 20; // Multiple matches
          reasoning += `${elements.length} elements match. `;
        }

        // Speed scoring
        if (duration < 100) {
          confidence += 20;
          reasoning += 'Fast selection. ';
        } else if (duration < 500) {
          confidence += 10;
          reasoning += 'Moderate selection speed. ';
        }

        // Selector type scoring
        if (selector.includes('text=') || selector.includes(':has-text')) {
          confidence += 15;
          reasoning += 'Text-based (stable). ';
        }

        if (selector.startsWith('[data-') || selector.includes('[aria-')) {
          confidence += 15;
          reasoning += 'Attribute-based (stable). ';
        }

        if (selector.startsWith('#') && !this.isDynamicId(selector.slice(1))) {
          confidence += 25;
          reasoning += 'Stable ID. ';
        }

        if (selector.startsWith('.') && !this.isDynamicClass(selector.slice(1))) {
          confidence += 15;
          reasoning += 'Stable class. ';
        }

        // Historical data
        const history = this.selectorHistory.get(selector);
        if (history && history.frequency > 5) {
          if (history.frequency / (history.frequency + 1) > this.stabilityThreshold) {
            confidence += 20;
            reasoning += 'Historically stable. ';
          }
        }

        // Determine stability level
        if (confidence >= 80) {
          stability = 'high';
        } else if (confidence >= 50) {
          stability = 'medium';
        }

        evaluations.push({
          selector,
          confidence,
          reasoning: reasoning.trim(),
          stability
        });

      } catch (error) {
        console.log('Selector evaluation failed', { selector, error });
      }
    }

    // Sort by confidence and return best
    evaluations.sort((a, b) => b.confidence - a.confidence);
    
    const best = evaluations[0];
    if (!best) {
      throw new SelectorAnalysisError('No viable selectors found');
    }

    return {
      selector: best.selector,
      confidence: best.confidence,
      reasoning: best.reasoning,
      fallbacks: evaluations.slice(1, 4).map(e => e.selector),
      stability: best.stability
    };
  }

  /**
   * Check if an ID appears to be dynamically generated
   */
  private isDynamicId(id: string): boolean {
    // Common dynamic ID patterns
    const dynamicPatterns = [
      /^ext-gen\d+$/,           // ExtJS generated IDs
      /^[a-f0-9]{8,}$/,         // Hex strings
      /^\d+$/,                  // Pure numbers
      /^id\d+$/,                // id + number
      /^component-\d+$/,        // component-number
      /^auto-\d+$/              // auto-number
    ];

    return dynamicPatterns.some(pattern => pattern.test(id));
  }

  /**
   * Check if a class appears to be dynamically generated
   */
  private isDynamicClass(className: string): boolean {
    const dynamicPatterns = [
      /^css-[a-z0-9]+$/,        // CSS-in-JS classes
      /^[a-f0-9]{6,}$/,         // Hex classes
      /^\w+-\d{6,}$/,           // word-number (long)
      /^generated-\w+$/         // generated-*
    ];

    return dynamicPatterns.some(pattern => pattern.test(className));
  }

  /**
   * Check if text content appears to be dynamic
   */
  private isDynamicText(text: string): boolean {
    const dynamicPatterns = [
      /^\d{4}-\d{2}-\d{2}/,     // Dates
      /^\d+:\d+/,               // Times
      /^Loading\.\.\./,         // Loading text
      /^\d+%$/,                 // Percentages
      /^[A-Z0-9]{8,}$/          // Random strings
    ];

    return dynamicPatterns.some(pattern => pattern.test(text));
  }

  /**
   * Check if a value appears to be dynamic
   */
  private isDynamicValue(value: string): boolean {
    return this.isDynamicId(value) || this.isDynamicText(value);
  }

  /**
   * Get all relevant elements from the page
   */
  private async getAllPageElements(
    page: Page, 
    depth: 'basic' | 'deep' | 'comprehensive'
  ): Promise<ElementHandle[]> {
    let selector = '';
    
    switch (depth) {
      case 'basic':
        selector = 'button, a, input, select, [role="button"], [onclick]';
        break;
      case 'deep':
        selector = 'button, a, input, select, div[onclick], span[onclick], [role="button"], [data-action], .clickable';
        break;
      case 'comprehensive':
        selector = '*[onclick], *[role], button, a, input, select, div, span, [data-*], [aria-*]';
        break;
    }

    return await page.locator(selector).elementHandles();
  }

  /**
   * Get comprehensive element attributes
   */
  private async getElementAttributes(element: ElementHandle): Promise<ElementAttributes> {
    const attributes = await element.evaluate((el: Element) => {
      const rect = el.getBoundingClientRect();
      const dataAttrs: Record<string, string> = {};

      // Extract data attributes
      for (const attr of Array.from(el.attributes)) {
        if (attr.name.startsWith('data-')) {
          dataAttrs[attr.name.slice(5)] = attr.value;
        }
      }

      return {
        id: el.id || undefined,
        className: el.className || undefined,
        tagName: el.tagName.toLowerCase(),
        textContent: el.textContent?.trim() || undefined,
        ariaLabel: el.getAttribute('aria-label') || undefined,
        dataAttributes: dataAttrs,
        position: { x: rect.x, y: rect.y },
        size: { width: rect.width, height: rect.height }
      };
    });

    return attributes;
  }

  /**
   * Analyze a single element
   */
  private async analyzeElement(page: Page, element: ElementHandle): Promise<SelectorAnalysis> {
    const attributes = await this.getElementAttributes(element);
    const alternatives = await this.generateSelectorAlternatives(page, element);
    
    // Determine stability based on attributes
    let stability: 'stable' | 'dynamic' | 'unreliable' = 'unreliable';
    
    if (attributes.id && !this.isDynamicId(attributes.id)) {
      stability = 'stable';
    } else if (attributes.textContent && !this.isDynamicText(attributes.textContent)) {
      stability = 'stable';
    } else if (attributes.className && !this.isDynamicClass(attributes.className)) {
      stability = 'dynamic';
    }

    return {
      element: alternatives[0] || `${attributes.tagName}`,
      stability,
      alternatives,
      confidence: stability === 'stable' ? 0.9 : stability === 'dynamic' ? 0.6 : 0.3,
      lastSeen: new Date(),
      frequency: 1,
      attributes,
      context: page.url()
    };
  }

  /**
   * Track selector usage for learning
   */
  async trackSelectorUsage(selector: string, success: boolean): Promise<void> {
    const existing = this.selectorHistory.get(selector);
    
    if (existing) {
      existing.frequency += 1;
      existing.lastSeen = new Date();
      if (success) {
        existing.confidence = Math.min(1.0, existing.confidence + 0.1);
      } else {
        existing.confidence = Math.max(0.0, existing.confidence - 0.2);
      }
    } else {
      // Create new entry (this would need element analysis)
      this.selectorHistory.set(selector, {
        element: selector,
        stability: success ? 'stable' : 'unreliable',
        alternatives: [selector],
        confidence: success ? 0.7 : 0.3,
        lastSeen: new Date(),
        frequency: 1,
        attributes: {} as ElementAttributes, // Would need to be populated
        context: ''
      });
    }
  }
}

import { Page } from 'playwright';
import { 
  LoadingStrategy, 
  LoadingCondition, 
  LoadingMetrics, 
  AdaptiveTimingData,
  SmartLoadingError 
} from '../types/index.js';

export class SmartLoader {
  private timingData: Map<string, AdaptiveTimingData> = new Map();
  private loadingHistory: LoadingMetrics[] = [];
  private maxHistorySize = 1000;

  constructor() {
  }

  /**
   * Intelligent loading with adaptive timing and fallbacks
   */
  async smartWait(
    page: Page,
    operation: string,
    context: string,
    strategy: 'adaptive' | 'race' | 'sequential' | 'intelligent' = 'intelligent',
    maxWait: number = 30000,
    customConditions?: LoadingCondition[]
  ): Promise<{ success: boolean; duration: number; strategy: string; conditions: string[] }> {
    const startTime = Date.now();
    
    try {
      console.log(`Starting smart wait for operation: ${operation} in context: ${context}`);

      // Get or create loading strategy
      const loadingStrategy = this.createLoadingStrategy(operation, context, strategy, maxWait, customConditions);
      
      // Execute the strategy
      const result = await this.executeLoadingStrategy(page, loadingStrategy);
      
      const duration = Date.now() - startTime;
      
      // Record metrics
      await this.recordLoadingMetrics({
        operation,
        duration,
        success: result.success,
        strategy: result.strategy,
        timestamp: new Date(),
        context,
        failureReason: result.success ? undefined : result.error
      });

      // Update adaptive timing data
      await this.updateAdaptiveTiming(operation, duration, result.success);

      console.log(`Smart wait completed: ${result.success ? 'SUCCESS' : 'FAILED'} in ${duration}ms`);

      return {
        success: result.success,
        duration,
        strategy: result.strategy,
        conditions: result.conditions
      };

    } catch (error) {
      const duration = Date.now() - startTime;
      
      await this.recordLoadingMetrics({
        operation,
        duration,
        success: false,
        strategy,
        timestamp: new Date(),
        context,
        failureReason: error instanceof Error ? error.message : 'Unknown error'
      });

      console.error(`Smart wait failed for ${operation}:`, error);
      throw new SmartLoadingError(`Smart wait failed for ${operation}`, { error, operation, context });
    }
  }

  /**
   * Create optimal loading strategy based on operation and historical data
   */
  private createLoadingStrategy(
    operation: string,
    context: string,
    strategy: string,
    maxWait: number,
    customConditions?: LoadingCondition[]
  ): LoadingStrategy {
    const timingData = this.timingData.get(operation);
    const baseTimeout = timingData ? Math.min(timingData.averageDuration * 2, maxWait) : maxWait;

    // Default conditions based on operation type
    let conditions: LoadingCondition[] = customConditions || [];

    if (conditions.length === 0) {
      conditions = this.getDefaultConditions(operation, context, baseTimeout);
    }

    // Determine timeouts based on strategy
    let timeouts: number[] = [];
    switch (strategy) {
      case 'race':
        timeouts = [baseTimeout * 0.5, baseTimeout];
        break;
      case 'sequential':
        timeouts = [baseTimeout * 0.3, baseTimeout * 0.7, baseTimeout];
        break;
      case 'adaptive':
        timeouts = timingData 
          ? [timingData.minDuration * 1.5, timingData.averageDuration * 1.5, timingData.maxDuration * 1.2]
          : [5000, 15000, maxWait];
        break;
      case 'intelligent':
      default:
        // Intelligent strategy combines the best of all approaches
        timeouts = this.calculateIntelligentTimeouts(operation, baseTimeout, maxWait);
        break;
    }

    return {
      type: strategy as any,
      timeouts,
      fallbacks: this.getFallbackStrategies(operation, context),
      conditions,
      context
    };
  }

  /**
   * Execute the loading strategy
   */
  private async executeLoadingStrategy(
    page: Page, 
    strategy: LoadingStrategy
  ): Promise<{ success: boolean; strategy: string; conditions: string[]; error?: string }> {
    const executedConditions: string[] = [];

    try {
      switch (strategy.type) {
        case 'race':
          return await this.executeRaceStrategy(page, strategy, executedConditions);
        case 'sequential':
          return await this.executeSequentialStrategy(page, strategy, executedConditions);
        case 'adaptive':
          return await this.executeAdaptiveStrategy(page, strategy, executedConditions);
        case 'intelligent':
        default:
          return await this.executeIntelligentStrategy(page, strategy, executedConditions);
      }
    } catch (error) {
      return {
        success: false,
        strategy: strategy.type,
        conditions: executedConditions,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Execute race strategy - first condition to complete wins
   */
  private async executeRaceStrategy(
    page: Page, 
    strategy: LoadingStrategy, 
    executedConditions: string[]
  ): Promise<{ success: boolean; strategy: string; conditions: string[] }> {
    const promises = strategy.conditions.map(async (condition, index) => {
      try {
        await this.waitForCondition(page, condition);
        executedConditions.push(`${condition.type}:${condition.selector || 'default'}`);
        return { success: true, index };
      } catch (error) {
        return { success: false, index, error };
      }
    });

    try {
      const result = await Promise.race(promises);
      return {
        success: result.success,
        strategy: 'race',
        conditions: executedConditions
      };
    } catch (error) {
      return {
        success: false,
        strategy: 'race',
        conditions: executedConditions
      };
    }
  }

  /**
   * Execute sequential strategy - try conditions in order
   */
  private async executeSequentialStrategy(
    page: Page, 
    strategy: LoadingStrategy, 
    executedConditions: string[]
  ): Promise<{ success: boolean; strategy: string; conditions: string[] }> {
    for (const condition of strategy.conditions) {
      try {
        await this.waitForCondition(page, condition);
        executedConditions.push(`${condition.type}:${condition.selector || 'default'}`);
        
        if (condition.required) {
          continue; // Must complete required conditions
        } else {
          return { success: true, strategy: 'sequential', conditions: executedConditions };
        }
      } catch (error) {
        if (condition.required) {
          return { success: false, strategy: 'sequential', conditions: executedConditions };
        }
        // Optional condition failed, continue
      }
    }

    return { success: true, strategy: 'sequential', conditions: executedConditions };
  }

  /**
   * Execute adaptive strategy - adjust based on page behavior
   */
  private async executeAdaptiveStrategy(
    page: Page, 
    strategy: LoadingStrategy, 
    executedConditions: string[]
  ): Promise<{ success: boolean; strategy: string; conditions: string[] }> {
    // Start with the most likely condition based on historical data
    const sortedConditions = [...strategy.conditions].sort((a, b) => {
      // Prioritize based on historical success for this context
      return this.getConditionPriority(b, strategy.context) - this.getConditionPriority(a, strategy.context);
    });

    for (const condition of sortedConditions) {
      try {
        await this.waitForCondition(page, condition);
        executedConditions.push(`${condition.type}:${condition.selector || 'default'}`);
        return { success: true, strategy: 'adaptive', conditions: executedConditions };
      } catch (error) {
        // Continue to next condition
        continue;
      }
    }

    return { success: false, strategy: 'adaptive', conditions: executedConditions };
  }

  /**
   * Execute intelligent strategy - combines multiple approaches
   */
  private async executeIntelligentStrategy(
    page: Page, 
    strategy: LoadingStrategy, 
    executedConditions: string[]
  ): Promise<{ success: boolean; strategy: string; conditions: string[] }> {
    // Phase 1: Quick checks (DOM content loaded)
    try {
      await this.waitForCondition(page, {
        type: 'domcontentloaded',
        timeout: 5000,
        required: false
      });
      executedConditions.push('domcontentloaded:quick');
    } catch (error) {
      // Continue even if quick check fails
    }

    // Phase 2: Race between network idle and specific elements
    const raceConditions = strategy.conditions.filter(c => 
      c.type === 'networkidle' || c.type === 'element'
    );

    if (raceConditions.length > 0) {
      try {
        const raceResult = await this.executeRaceStrategy(page, {
          ...strategy,
          conditions: raceConditions
        }, executedConditions);
        
        if (raceResult.success) {
          return { success: true, strategy: 'intelligent', conditions: executedConditions };
        }
      } catch (error) {
        // Continue to fallback
      }
    }

    // Phase 3: Fallback to any remaining conditions
    const fallbackConditions = strategy.conditions.filter(c => 
      c.type !== 'networkidle' && c.type !== 'element'
    );

    for (const condition of fallbackConditions) {
      try {
        await this.waitForCondition(page, condition);
        executedConditions.push(`${condition.type}:${condition.selector || 'default'}`);
        return { success: true, strategy: 'intelligent', conditions: executedConditions };
      } catch (error) {
        continue;
      }
    }

    return { success: false, strategy: 'intelligent', conditions: executedConditions };
  }

  /**
   * Wait for a specific condition
   */
  private async waitForCondition(page: Page, condition: LoadingCondition): Promise<void> {
    switch (condition.type) {
      case 'domcontentloaded':
        await page.waitForLoadState('domcontentloaded', { timeout: condition.timeout });
        break;
      case 'networkidle':
        await page.waitForLoadState('networkidle', { timeout: condition.timeout });
        break;
      case 'element':
        if (!condition.selector) {
          throw new Error('Element condition requires selector');
        }
        await page.waitForSelector(condition.selector, { timeout: condition.timeout });
        break;
      case 'custom':
        // Custom conditions would be implemented based on specific needs
        await page.waitForTimeout(Math.min(condition.timeout, 1000));
        break;
      default:
        throw new Error(`Unknown condition type: ${condition.type}`);
    }
  }

  /**
   * Get default conditions based on operation and context
   */
  private getDefaultConditions(operation: string, context: string, baseTimeout: number): LoadingCondition[] {
    const conditions: LoadingCondition[] = [];

    // BiNDup-specific conditions
    if (context.includes('bindui') || context.includes('bindcloud')) {
      if (operation.includes('launch')) {
        conditions.push(
          { type: 'domcontentloaded', timeout: 5000, required: false },
          { type: 'networkidle', timeout: baseTimeout, required: false },
          { type: 'element', selector: 'button:has-text("閉じる")', timeout: 10000, required: false }
        );
      } else if (operation.includes('navigate')) {
        conditions.push(
          { type: 'domcontentloaded', timeout: 3000, required: false },
          { type: 'networkidle', timeout: baseTimeout * 0.7, required: false },
          { type: 'element', selector: '.cs-item[draggable="true"]', timeout: 15000, required: false }
        );
      } else if (operation.includes('edit')) {
        conditions.push(
          { type: 'domcontentloaded', timeout: 2000, required: false },
          { type: 'element', selector: 'iframe[name="preview"]', timeout: 10000, required: false },
          { type: 'networkidle', timeout: baseTimeout, required: false }
        );
      }
    }

    // Default fallback conditions
    if (conditions.length === 0) {
      conditions.push(
        { type: 'domcontentloaded', timeout: 5000, required: false },
        { type: 'networkidle', timeout: baseTimeout, required: false }
      );
    }

    return conditions;
  }

  /**
   * Calculate intelligent timeouts based on historical data
   */
  private calculateIntelligentTimeouts(operation: string, baseTimeout: number, maxWait: number): number[] {
    const timingData = this.timingData.get(operation);
    
    if (!timingData || timingData.sampleCount < 3) {
      // Not enough data, use conservative defaults
      return [3000, 10000, Math.min(baseTimeout, maxWait)];
    }

    const quick = Math.max(1000, timingData.minDuration * 1.2);
    const normal = Math.max(quick * 2, timingData.averageDuration * 1.5);
    const extended = Math.min(maxWait, Math.max(normal * 2, timingData.maxDuration * 1.2));

    return [quick, normal, extended];
  }

  /**
   * Get fallback strategies for different operations
   */
  private getFallbackStrategies(operation: string, context: string): string[] {
    const fallbacks: string[] = [];

    if (context.includes('bindui')) {
      fallbacks.push('force-click', 'retry-navigation', 'popup-handling');
    }

    fallbacks.push('timeout-extension', 'element-polling');
    return fallbacks;
  }

  /**
   * Get condition priority based on historical success
   */
  private getConditionPriority(condition: LoadingCondition, context: string): number {
    // This would be based on historical data
    // For now, return default priorities
    switch (condition.type) {
      case 'element': return 3;
      case 'domcontentloaded': return 2;
      case 'networkidle': return 1;
      default: return 0;
    }
  }

  /**
   * Record loading metrics for learning
   */
  private async recordLoadingMetrics(metrics: LoadingMetrics): Promise<void> {
    this.loadingHistory.push(metrics);
    
    // Keep history size manageable
    if (this.loadingHistory.length > this.maxHistorySize) {
      this.loadingHistory = this.loadingHistory.slice(-this.maxHistorySize);
    }
  }

  /**
   * Update adaptive timing data
   */
  private async updateAdaptiveTiming(operation: string, duration: number, success: boolean): Promise<void> {
    const existing = this.timingData.get(operation);
    
    if (existing) {
      const newSampleCount = existing.sampleCount + 1;
      const newAverage = (existing.averageDuration * existing.sampleCount + duration) / newSampleCount;
      
      this.timingData.set(operation, {
        operation,
        averageDuration: newAverage,
        minDuration: Math.min(existing.minDuration, duration),
        maxDuration: Math.max(existing.maxDuration, duration),
        successRate: success 
          ? (existing.successRate * existing.sampleCount + 1) / newSampleCount
          : (existing.successRate * existing.sampleCount) / newSampleCount,
        sampleCount: newSampleCount,
        lastUpdated: new Date()
      });
    } else {
      this.timingData.set(operation, {
        operation,
        averageDuration: duration,
        minDuration: duration,
        maxDuration: duration,
        successRate: success ? 1.0 : 0.0,
        sampleCount: 1,
        lastUpdated: new Date()
      });
    }
  }

  /**
   * Get timing recommendations for an operation
   */
  getTimingRecommendations(operation: string): AdaptiveTimingData | null {
    return this.timingData.get(operation) || null;
  }

  /**
   * Get loading history for analysis
   */
  getLoadingHistory(operation?: string): LoadingMetrics[] {
    if (operation) {
      return this.loadingHistory.filter(m => m.operation === operation);
    }
    return [...this.loadingHistory];
  }
}

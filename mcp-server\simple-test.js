#!/usr/bin/env node

/**
 * Simple test to verify the MCP server can start and respond to basic requests
 */

import { spawn } from 'child_process';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

async function testServer() {
  console.log('🚀 Testing Playwright MCP Server...\n');

  try {
    // Test 1: Server can start
    console.log('📋 Test 1: Server Startup');
    const serverPath = join(__dirname, 'dist', 'index.js');
    
    const server = spawn('node', [serverPath], {
      stdio: ['pipe', 'pipe', 'pipe']
    });

    let output = '';
    let errorOutput = '';

    server.stdout.on('data', (data) => {
      output += data.toString();
    });

    server.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });

    // Test 2: Send a simple tools/list request
    console.log('📋 Test 2: Tools List Request');
    
    const listToolsMessage = {
      jsonrpc: '2.0',
      id: 1,
      method: 'tools/list',
      params: {}
    };

    server.stdin.write(JSON.stringify(listToolsMessage) + '\n');

    // Wait for response
    await new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        server.kill();
        reject(new Error('Server response timeout'));
      }, 10000);

      server.on('close', (code) => {
        clearTimeout(timeout);
        
        console.log('📊 Server Output:');
        console.log(output);
        
        if (errorOutput) {
          console.log('📊 Server Errors:');
          console.log(errorOutput);
        }

        if (code === 0 || output.includes('tools')) {
          console.log('✅ Server test passed!');
          console.log('✅ MCP Server is working correctly');
          resolve();
        } else {
          reject(new Error(`Server exited with code ${code}`));
        }
      });

      // Send end signal after a short delay
      setTimeout(() => {
        server.stdin.end();
      }, 2000);
    });

  } catch (error) {
    console.error('❌ Server test failed:', error.message);
    process.exit(1);
  }
}

testServer().catch(console.error);

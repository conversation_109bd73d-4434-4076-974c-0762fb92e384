const { Page, FrameLocator } = require('@playwright/test');
const { TestLogger } = require('./test-logger');

/**
 * Enhanced Loading State Detector
 * Provides comprehensive loading state detection to prevent operations
 * during unstable page transitions that cause closures in BiNDup
 */
export class EnhancedLoadingStateDetector {
  private page: Page;
  private detectionStrategies: LoadingDetectionStrategy[] = [];
  private customIndicators: string[] = [];

  constructor(page: Page) {
    this.page = page;
    this.initializeDetectionStrategies();
    TestLogger.logStep('🔍 Enhanced Loading State Detector initialized', 'success');
  }

  /**
   * Check if page is currently in a loading state
   */
  async isPageLoading(): Promise<boolean> {
    TestLogger.logStep('🔍 Checking page loading state', 'start');

    try {
      for (const strategy of this.detectionStrategies) {
        const isLoading = await strategy.detect(this.page);
        
        if (isLoading) {
          TestLogger.logStep(`⏳ Loading detected by: ${strategy.name}`, 'warning');
          return true;
        }
      }

      TestLogger.logStep('✅ No loading state detected', 'success');
      return false;

    } catch (error) {
      TestLogger.logStep(`⚠️ Loading state detection error: ${error}`, 'warning');
      return false; // Assume not loading if detection fails
    }
  }

  /**
   * Wait for all loading states to complete
   */
  async waitForLoadingComplete(timeoutMs: number = 30000): Promise<boolean> {
    TestLogger.logStep('⏳ Waiting for all loading states to complete', 'start');

    const startTime = Date.now();
    let lastLoadingStrategy = '';

    while (Date.now() - startTime < timeoutMs) {
      const isLoading = await this.isPageLoading();
      
      if (!isLoading) {
        TestLogger.logStep('✅ All loading states completed', 'success');
        return true;
      }

      // Log which strategy is still detecting loading
      for (const strategy of this.detectionStrategies) {
        const isStrategyLoading = await strategy.detect(this.page);
        if (isStrategyLoading && lastLoadingStrategy !== strategy.name) {
          TestLogger.logStep(`⏳ Still loading: ${strategy.name}`, 'warning');
          lastLoadingStrategy = strategy.name;
        }
      }

      await this.page.waitForTimeout(1000);
    }

    TestLogger.logStep('⚠️ Loading state timeout reached', 'warning');
    return false;
  }

  /**
   * Check if iframe is in loading state
   */
  async isIframeLoading(iframe: FrameLocator): Promise<boolean> {
    TestLogger.logStep('🔍 Checking iframe loading state', 'start');

    try {
      const frame = iframe.contentFrame();
      if (!frame) {
        TestLogger.logStep('⚠️ Iframe not accessible', 'warning');
        return true; // Consider as loading if not accessible
      }

      // Check iframe document ready state
      const readyState = await frame.evaluate(() => document.readyState);
      if (readyState !== 'complete') {
        TestLogger.logStep(`⏳ Iframe document not ready: ${readyState}`, 'warning');
        return true;
      }

      // Check for iframe-specific loading indicators
      const iframeLoadingSelectors = [
        '.loading',
        '.spinner',
        '.cs-loading',
        'text=読み込み中',
        'text=処理中'
      ];

      for (const selector of iframeLoadingSelectors) {
        try {
          const isVisible = await iframe.locator(selector).isVisible({ timeout: 1000 });
          if (isVisible) {
            TestLogger.logStep(`⏳ Iframe loading indicator found: ${selector}`, 'warning');
            return true;
          }
        } catch (error) {
          // Selector not found, continue
        }
      }

      TestLogger.logStep('✅ Iframe not in loading state', 'success');
      return false;

    } catch (error) {
      TestLogger.logStep(`⚠️ Iframe loading check error: ${error}`, 'warning');
      return true; // Consider as loading if check fails
    }
  }

  /**
   * Wait for iframe loading to complete
   */
  async waitForIframeLoadingComplete(iframe: FrameLocator, timeoutMs: number = 20000): Promise<boolean> {
    TestLogger.logStep('⏳ Waiting for iframe loading to complete', 'start');

    const startTime = Date.now();

    while (Date.now() - startTime < timeoutMs) {
      const isLoading = await this.isIframeLoading(iframe);
      
      if (!isLoading) {
        TestLogger.logStep('✅ Iframe loading completed', 'success');
        return true;
      }

      await this.page.waitForTimeout(1000);
    }

    TestLogger.logStep('⚠️ Iframe loading timeout reached', 'warning');
    return false;
  }

  /**
   * Check for BiNDup-specific loading states
   */
  async isBiNDupLoading(): Promise<boolean> {
    TestLogger.logStep('🔍 Checking BiNDup-specific loading states', 'start');

    try {
      // BiNDup-specific loading indicators
      const bindupLoadingSelectors = [
        '.x-mask',                    // ExtJS loading mask
        '.x-mask-loading',            // ExtJS loading mask with loading class
        '.cs-loading',                // Custom loading indicator
        '.loading-mask',              // Generic loading mask
        'text=読み込み中',             // Japanese loading text
        'text=処理中',                // Japanese processing text
        'text=保存中',                // Japanese saving text
        '[data-loading="true"]',      // Data attribute loading
        '.spinner',                   // Generic spinner
        '.progress-bar'               // Progress indicators
      ];

      for (const selector of bindupLoadingSelectors) {
        try {
          const isVisible = await this.page.locator(selector).isVisible({ timeout: 1000 });
          if (isVisible) {
            TestLogger.logStep(`⏳ BiNDup loading indicator found: ${selector}`, 'warning');
            return true;
          }
        } catch (error) {
          // Selector not found, continue
        }
      }

      // Check for network activity
      const hasNetworkActivity = await this.checkNetworkActivity();
      if (hasNetworkActivity) {
        TestLogger.logStep('⏳ Network activity detected', 'warning');
        return true;
      }

      TestLogger.logStep('✅ BiNDup not in loading state', 'success');
      return false;

    } catch (error) {
      TestLogger.logStep(`⚠️ BiNDup loading check error: ${error}`, 'warning');
      return false;
    }
  }

  /**
   * Add custom loading indicator
   */
  addCustomIndicator(selector: string): void {
    this.customIndicators.push(selector);
    TestLogger.logStep(`📝 Custom loading indicator added: ${selector}`, 'success');
  }

  /**
   * Check for custom loading indicators
   */
  async checkCustomIndicators(): Promise<boolean> {
    for (const selector of this.customIndicators) {
      try {
        const isVisible = await this.page.locator(selector).isVisible({ timeout: 1000 });
        if (isVisible) {
          TestLogger.logStep(`⏳ Custom loading indicator found: ${selector}`, 'warning');
          return true;
        }
      } catch (error) {
        // Selector not found, continue
      }
    }

    return false;
  }

  /**
   * Smart wait that combines multiple loading detection strategies
   */
  async smartWaitForStability(timeoutMs: number = 30000): Promise<boolean> {
    TestLogger.logStep('🧠 Smart wait for page stability', 'start');

    const startTime = Date.now();
    let consecutiveStableChecks = 0;
    const requiredStableChecks = 3; // Require 3 consecutive stable checks

    while (Date.now() - startTime < timeoutMs) {
      // Check all loading states
      const isPageLoading = await this.isPageLoading();
      const isBiNDupLoading = await this.isBiNDupLoading();
      const hasCustomLoading = await this.checkCustomIndicators();

      if (!isPageLoading && !isBiNDupLoading && !hasCustomLoading) {
        consecutiveStableChecks++;
        
        if (consecutiveStableChecks >= requiredStableChecks) {
          TestLogger.logStep('✅ Page stability confirmed with smart wait', 'success');
          return true;
        }
        
        TestLogger.logStep(`🔍 Stable check ${consecutiveStableChecks}/${requiredStableChecks}`, 'start');
      } else {
        consecutiveStableChecks = 0; // Reset counter if loading detected
      }

      await this.page.waitForTimeout(1000);
    }

    TestLogger.logStep('⚠️ Smart wait timeout reached', 'warning');
    return false;
  }

  /**
   * Check for network activity (simplified)
   */
  private async checkNetworkActivity(): Promise<boolean> {
    try {
      // Check if there are any pending requests
      const pendingRequests = await this.page.evaluate(() => {
        // Simple check for XMLHttpRequest activity
        return (window as any).XMLHttpRequest && 
               (window as any).XMLHttpRequest.prototype._activeRequests > 0;
      });

      return pendingRequests || false;

    } catch (error) {
      // Network activity check failed, assume no activity
      return false;
    }
  }

  /**
   * Initialize detection strategies
   */
  private initializeDetectionStrategies(): void {
    // Strategy 1: Document ready state
    this.detectionStrategies.push({
      name: 'Document Ready State',
      detect: async (page: Page) => {
        const readyState = await page.evaluate(() => document.readyState);
        return readyState !== 'complete';
      }
    });

    // Strategy 2: Loading indicators
    this.detectionStrategies.push({
      name: 'Loading Indicators',
      detect: async (page: Page) => {
        const loadingSelectors = ['.loading', '.spinner', '.progress'];
        
        for (const selector of loadingSelectors) {
          try {
            const isVisible = await page.locator(selector).isVisible({ timeout: 500 });
            if (isVisible) return true;
          } catch (error) {
            // Continue
          }
        }
        return false;
      }
    });

    // Strategy 3: ExtJS masks
    this.detectionStrategies.push({
      name: 'ExtJS Masks',
      detect: async (page: Page) => {
        try {
          const maskVisible = await page.locator('.x-mask').isVisible({ timeout: 500 });
          return maskVisible;
        } catch (error) {
          return false;
        }
      }
    });

    // Strategy 4: JavaScript execution state
    this.detectionStrategies.push({
      name: 'JavaScript Execution',
      detect: async (page: Page) => {
        try {
          const isExecuting = await page.evaluate(() => {
            // Check if there are any running timers or intervals
            return (window as any)._activeTimers > 0 || 
                   (window as any)._activeIntervals > 0;
          });
          return isExecuting || false;
        } catch (error) {
          return false;
        }
      }
    });

    TestLogger.logStep(`📋 Initialized ${this.detectionStrategies.length} loading detection strategies`, 'success');
  }
}

/**
 * Loading Detection Strategy Interface
 */
interface LoadingDetectionStrategy {
  name: string;
  detect: (page: Page) => Promise<boolean>;
}

// Export for CommonJS
module.exports = { EnhancedLoadingStateDetector };

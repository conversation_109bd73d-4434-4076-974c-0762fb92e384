# Playwright MCP Server

An intelligent Model Context Protocol (MCP) server for Playwright automation with BiNDup-specific optimizations.

## Features

### 🔍 Selector Intelligence
- **Stability Analysis**: Automatically detect stable vs dynamic selectors
- **Alternative Generation**: Generate multiple robust selector alternatives
- **BiNDup-Specific Patterns**: Built-in knowledge of BiNDup element patterns
- **Confidence Scoring**: Rate selector reliability based on historical data

### ⏱️ Smart Loading
- **Adaptive Timing**: Learn optimal wait times from historical data
- **Race Conditions**: Use multiple loading strategies simultaneously
- **Intelligent Fallbacks**: Automatic fallback strategies for failed waits
- **BiNDup Optimization**: Specialized loading patterns for BiNDup operations

### 🐛 Advanced Debugging
- **Flow Snapshots**: Capture comprehensive page state at each step
- **Visual Highlighting**: Highlight elements for visual debugging
- **Network Monitoring**: Track network activity and performance
- **Insight Generation**: Automatic analysis and recommendations

### 🎯 BiNDup Intelligence
- **Popup Handling**: Automatic detection and handling of BiNDup popups
- **Loading State Detection**: Intelligent detection of BiNDup loading states
- **Page Closure Prevention**: Predict and prevent page closure issues
- **Operation Optimization**: Specialized optimizations for BiNDup workflows

## Installation

```bash
cd mcp-server
npm install
npm run build
```

## Usage

### Starting the Server

```bash
npm start
```

### Available Tools

#### 1. `playwright-analyze-selectors`
Analyze page selectors for stability and suggest alternatives.

```json
{
  "name": "playwright-analyze-selectors",
  "arguments": {
    "analysisDepth": "comprehensive",
    "targetElement": "#button-1014",
    "context": "bindui-popup"
  }
}
```

#### 2. `playwright-smart-wait`
Intelligent waiting with adaptive timing.

```json
{
  "name": "playwright-smart-wait",
  "arguments": {
    "operation": "bindui-launch",
    "context": "site-theater-navigation",
    "strategy": "intelligent",
    "maxWait": 30000
  }
}
```

#### 3. `playwright-debug-flow`
Comprehensive flow debugging with visual feedback.

```json
{
  "name": "playwright-debug-flow",
  "arguments": {
    "step": "BiNDup Launch",
    "captureLevel": "detailed",
    "highlightElements": ["#button-1014", ".cs-item"],
    "includeScreenshot": true
  }
}
```

#### 4. `playwright-bindui-optimize`
BiNDup-specific optimizations.

```json
{
  "name": "playwright-bindui-optimize",
  "arguments": {
    "operation": "launch",
    "optimizationLevel": "balanced",
    "context": "site-editor-access"
  }
}
```

#### 5. `playwright-init-browser`
Initialize Playwright browser.

```json
{
  "name": "playwright-init-browser",
  "arguments": {
    "headless": false,
    "url": "https://mypage.weblife.me/auth/"
  }
}
```

#### 6. `playwright-close-browser`
Close browser and cleanup.

```json
{
  "name": "playwright-close-browser",
  "arguments": {}
}
```

## Integration with Tests

### Before (Manual Approach)
```typescript
// Old approach with manual timeouts and selectors
await page.waitForLoadState('networkidle'); // Often fails
await page.locator('#button-1014').click(); // Dynamic ID might fail
```

### After (MCP-Powered)
```typescript
// New approach with intelligent automation
await mcpClient.invoke('playwright-smart-wait', {
  operation: 'bindui-launch',
  context: 'site-theater-navigation',
  strategy: 'intelligent'
});

const selectorResult = await mcpClient.invoke('playwright-analyze-selectors', {
  targetElement: 'popup-close-button',
  analysisDepth: 'comprehensive'
});

await page.locator(selectorResult.recommendations[0].selector).click();
```

## Expected Benefits

### Performance Improvements
- **50-70% reduction** in test execution time
- **90%+ success rate** for BiNDup navigation
- **Automatic optimization** of wait times

### Stability Improvements
- **Intelligent selector fallbacks** for dynamic elements
- **Predictive page closure prevention**
- **Adaptive timing** based on actual application behavior

### Debug Improvements
- **Visual flow documentation** with screenshots
- **Real-time element highlighting**
- **Comprehensive failure analysis**

## Configuration

The server can be configured by modifying the `config` object in `src/index.ts`:

```typescript
{
  playwright: {
    headless: false,
    timeout: 30000,
    viewport: { width: 1920, height: 1080 }
  },
  bindui: {
    baseUrl: 'https://edit3.bindcloud.jp',
    defaultTimeouts: {
      launch: 30000,
      navigation: 20000,
      element: 10000
    }
  }
}
```

## Development

### Building
```bash
npm run build
```

### Development Mode
```bash
npm run dev
```

### Testing
```bash
npm test
```

## Architecture

```
mcp-server/
├── src/
│   ├── modules/
│   │   ├── SelectorAnalyzer.ts    # Selector intelligence
│   │   ├── SmartLoader.ts         # Adaptive loading
│   │   ├── BiNDupIntelligence.ts  # BiNDup-specific logic
│   │   └── DebugLogger.ts         # Debug and logging
│   ├── types/
│   │   └── index.ts               # Type definitions
│   └── index.ts                   # Main MCP server
├── package.json
├── tsconfig.json
└── README.md
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details.

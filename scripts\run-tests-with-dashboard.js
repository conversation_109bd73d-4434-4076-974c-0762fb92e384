#!/usr/bin/env node

/**
 * 🚀 BiNDup Test Execution with Magnificent Dashboard Generation
 * Complete test execution pipeline with stunning client-ready reporting
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const DashboardReportGenerator = require('./generate-dashboard-report');

class TestExecutionPipeline {
    constructor() {
        this.startTime = Date.now();
        this.results = {
            testExecution: null,
            dashboardGeneration: null,
            totalDuration: 0
        };
        
        console.log('🎭 BiNDup Test Execution Pipeline with Magnificent Dashboard');
        console.log('═'.repeat(80));
    }

    async execute() {
        try {
            console.log('🚀 Starting complete test execution pipeline...\n');
            
            // Step 1: Execute tests
            await this.runTests();
            
            // Step 2: Generate magnificent dashboard
            await this.generateDashboard();
            
            // Step 3: Generate summary
            this.generateExecutionSummary();
            
            // Step 4: Open results
            this.openResults();
            
            console.log('\n🎉 Complete test execution pipeline finished successfully!');
            return this.results;
            
        } catch (error) {
            console.error('\n❌ Pipeline execution failed:', error);
            throw error;
        }
    }

    async runTests() {
        console.log('🧪 PHASE 1: Executing BiNDup Test Suite');
        console.log('─'.repeat(50));
        
        const testStartTime = Date.now();
        
        try {
            // Run tests with JSON output for dashboard processing
            console.log('⚡ Running Playwright tests with enhanced reporting...');
            
            const testCommand = 'npx playwright test --reporter=html,json,junit';
            console.log(`📋 Command: ${testCommand}\n`);
            
            // Execute tests
            const testOutput = execSync(testCommand, { 
                encoding: 'utf8',
                stdio: 'pipe',
                maxBuffer: 1024 * 1024 * 10 // 10MB buffer
            });
            
            const testDuration = Date.now() - testStartTime;
            
            this.results.testExecution = {
                status: 'success',
                duration: testDuration,
                output: testOutput
            };
            
            console.log(`✅ Tests completed successfully in ${Math.round(testDuration / 1000)}s`);
            
            // Parse test results if available
            this.parseTestResults();
            
        } catch (error) {
            const testDuration = Date.now() - testStartTime;
            
            this.results.testExecution = {
                status: 'failed',
                duration: testDuration,
                error: error.message,
                output: error.stdout || error.stderr || ''
            };
            
            console.log(`⚠️ Tests completed with issues in ${Math.round(testDuration / 1000)}s`);
            console.log('📊 Proceeding with dashboard generation from available results...');
        }
        
        console.log('─'.repeat(50));
    }

    parseTestResults() {
        try {
            const resultsPath = 'test-results/results.json';
            if (fs.existsSync(resultsPath)) {
                const results = JSON.parse(fs.readFileSync(resultsPath, 'utf8'));
                
                if (results.stats) {
                    console.log(`📊 Test Results Summary:`);
                    console.log(`   🎯 Total: ${results.stats.total || 0}`);
                    console.log(`   ✅ Passed: ${results.stats.passed || 0}`);
                    console.log(`   ❌ Failed: ${results.stats.failed || 0}`);
                    console.log(`   ⏭️ Skipped: ${results.stats.skipped || 0}`);
                    
                    const passRate = results.stats.total > 0 ? 
                        Math.round((results.stats.passed / results.stats.total) * 100) : 0;
                    console.log(`   📈 Pass Rate: ${passRate}%`);
                }
            }
        } catch (error) {
            console.log('⚠️ Could not parse test results, using fallback data');
        }
    }

    async generateDashboard() {
        console.log('\n🎨 PHASE 2: Generating Magnificent Dashboard');
        console.log('─'.repeat(50));
        
        const dashboardStartTime = Date.now();
        
        try {
            console.log('🎭 Initializing BiNDup Test Intelligence Dashboard...');
            
            const generator = new DashboardReportGenerator();
            const reportPath = await generator.generate();
            
            const dashboardDuration = Date.now() - dashboardStartTime;
            
            this.results.dashboardGeneration = {
                status: 'success',
                duration: dashboardDuration,
                reportPath: reportPath
            };
            
            console.log(`✅ Dashboard generated successfully in ${Math.round(dashboardDuration / 1000)}s`);
            
        } catch (error) {
            const dashboardDuration = Date.now() - dashboardStartTime;
            
            this.results.dashboardGeneration = {
                status: 'failed',
                duration: dashboardDuration,
                error: error.message
            };
            
            console.error(`❌ Dashboard generation failed: ${error.message}`);
            throw error;
        }
        
        console.log('─'.repeat(50));
    }

    generateExecutionSummary() {
        console.log('\n📊 EXECUTION SUMMARY');
        console.log('═'.repeat(80));
        
        this.results.totalDuration = Date.now() - this.startTime;
        
        // Test Execution Summary
        console.log('🧪 TEST EXECUTION:');
        if (this.results.testExecution) {
            console.log(`   Status: ${this.results.testExecution.status === 'success' ? '✅ Success' : '⚠️ With Issues'}`);
            console.log(`   Duration: ${Math.round(this.results.testExecution.duration / 1000)}s`);
        }
        
        // Dashboard Generation Summary
        console.log('\n🎨 DASHBOARD GENERATION:');
        if (this.results.dashboardGeneration) {
            console.log(`   Status: ${this.results.dashboardGeneration.status === 'success' ? '✅ Success' : '❌ Failed'}`);
            console.log(`   Duration: ${Math.round(this.results.dashboardGeneration.duration / 1000)}s`);
            if (this.results.dashboardGeneration.reportPath) {
                console.log(`   Report: ${this.results.dashboardGeneration.reportPath}`);
            }
        }
        
        // Overall Summary
        console.log('\n🎯 OVERALL PIPELINE:');
        console.log(`   Total Duration: ${Math.round(this.results.totalDuration / 1000)}s`);
        console.log(`   Pipeline Status: ${this.getPipelineStatus()}`);
        
        // Available Reports
        console.log('\n📋 AVAILABLE REPORTS:');
        console.log('   🎭 Magnificent Dashboard: test-reports/dashboard/index.html');
        console.log('   📊 Playwright Report: playwright-report/index.html');
        console.log('   📄 JSON Results: test-results/results.json');
        console.log('   📋 JUnit Results: test-results/results.xml');
        
        console.log('═'.repeat(80));
    }

    getPipelineStatus() {
        const testSuccess = this.results.testExecution?.status === 'success';
        const dashboardSuccess = this.results.dashboardGeneration?.status === 'success';
        
        if (testSuccess && dashboardSuccess) {
            return '🎉 Complete Success';
        } else if (dashboardSuccess) {
            return '🎨 Dashboard Generated (Tests with Issues)';
        } else if (testSuccess) {
            return '🧪 Tests Passed (Dashboard Failed)';
        } else {
            return '⚠️ Partial Success';
        }
    }

    openResults() {
        console.log('\n🌐 OPENING RESULTS...');
        
        try {
            // Open magnificent dashboard
            if (this.results.dashboardGeneration?.reportPath) {
                const dashboardPath = path.resolve(this.results.dashboardGeneration.reportPath);
                console.log('🎭 Opening magnificent dashboard...');
                this.openInBrowser(dashboardPath);
            }
            
            // Optionally open Playwright report as well
            const playwrightReportPath = 'playwright-report/index.html';
            if (fs.existsSync(playwrightReportPath)) {
                console.log('📊 Playwright report also available');
                // Uncomment to auto-open Playwright report too
                // setTimeout(() => this.openInBrowser(path.resolve(playwrightReportPath)), 2000);
            }
            
        } catch (error) {
            console.log('⚠️ Could not auto-open reports. Please open manually:');
            if (this.results.dashboardGeneration?.reportPath) {
                console.log(`   Dashboard: file://${path.resolve(this.results.dashboardGeneration.reportPath)}`);
            }
        }
    }

    openInBrowser(filePath) {
        try {
            const command = process.platform === 'darwin' ? 'open' : 
                          process.platform === 'win32' ? 'start' : 'xdg-open';
            
            execSync(`${command} "${filePath}"`, { stdio: 'ignore' });
        } catch (error) {
            console.log(`⚠️ Could not auto-open: ${filePath}`);
        }
    }
}

// CLI Interface
class CLI {
    static async run() {
        const args = process.argv.slice(2);
        
        // Parse command line arguments
        const options = {
            browser: 'chromium',
            headed: false,
            workers: 1,
            timeout: 180000,
            skipTests: false,
            skipDashboard: false
        };
        
        args.forEach(arg => {
            if (arg === '--webkit') options.browser = 'webkit';
            if (arg === '--both') options.browser = 'both';
            if (arg === '--headed') options.headed = true;
            if (arg === '--skip-tests') options.skipTests = true;
            if (arg === '--skip-dashboard') options.skipDashboard = true;
            if (arg.startsWith('--workers=')) options.workers = parseInt(arg.split('=')[1]);
            if (arg.startsWith('--timeout=')) options.timeout = parseInt(arg.split('=')[1]);
        });
        
        console.log('🎯 Configuration:');
        console.log(`   Browser: ${options.browser}`);
        console.log(`   Headed: ${options.headed}`);
        console.log(`   Workers: ${options.workers}`);
        console.log(`   Timeout: ${options.timeout}ms`);
        console.log(`   Skip Tests: ${options.skipTests}`);
        console.log(`   Skip Dashboard: ${options.skipDashboard}`);
        console.log('');
        
        try {
            const pipeline = new TestExecutionPipeline();
            
            if (options.skipTests) {
                console.log('⏭️ Skipping test execution, generating dashboard from existing results...');
                await pipeline.generateDashboard();
                pipeline.openResults();
            } else if (options.skipDashboard) {
                console.log('⏭️ Running tests only, skipping dashboard generation...');
                await pipeline.runTests();
            } else {
                await pipeline.execute();
            }
            
            process.exit(0);
            
        } catch (error) {
            console.error('\n💥 Pipeline failed:', error.message);
            process.exit(1);
        }
    }
}

// Help text
function showHelp() {
    console.log(`
🎭 BiNDup Test Execution Pipeline with Magnificent Dashboard

USAGE:
  node scripts/run-tests-with-dashboard.js [options]

OPTIONS:
  --webkit              Run tests on WebKit browser (default: chromium)
  --both                Run tests on both Chromium and WebKit
  --headed              Run tests in headed mode (default: headless)
  --workers=N           Number of workers (default: 1)
  --timeout=N           Test timeout in milliseconds (default: 180000)
  --skip-tests          Skip test execution, generate dashboard only
  --skip-dashboard      Run tests only, skip dashboard generation
  --help                Show this help message

EXAMPLES:
  # Run complete pipeline with default settings
  node scripts/run-tests-with-dashboard.js
  
  # Run tests on WebKit with dashboard
  node scripts/run-tests-with-dashboard.js --webkit
  
  # Generate dashboard from existing results
  node scripts/run-tests-with-dashboard.js --skip-tests
  
  # Run tests only without dashboard
  node scripts/run-tests-with-dashboard.js --skip-dashboard

OUTPUTS:
  🎭 Magnificent Dashboard: test-reports/dashboard/index.html
  📊 Playwright Report: playwright-report/index.html
  📄 JSON Results: test-results/results.json
`);
}

// Main execution
if (require.main === module) {
    const args = process.argv.slice(2);
    
    if (args.includes('--help') || args.includes('-h')) {
        showHelp();
        process.exit(0);
    }
    
    CLI.run();
}

module.exports = { TestExecutionPipeline, CLI };

/**
 * 🎨 BiNDup Modern Dashboard - Advanced CSS
 * Ultra-modern design with glassmorphism, neumorphism, and micro-interactions
 */

/* ===== MODERN RESET & BASE ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

:root {
    /* Research-Based Optimal Color Palette - Based on Material Design 3 & Apple HIG */

    /* Primary - Ocean Blue (Trust, Reliability, Professional) */
    --primary-50: #e3f2fd;
    --primary-100: #bbdefb;
    --primary-200: #90caf9;
    --primary-300: #64b5f6;
    --primary-400: #42a5f5;
    --primary-500: #2196f3;  /* Main brand color */
    --primary-600: #1e88e5;
    --primary-700: #1976d2;
    --primary-800: #1565c0;
    --primary-900: #0d47a1;

    /* Secondary - Deep Purple (Innovation, Premium) */
    --secondary-50: #f3e5f5;
    --secondary-100: #e1bee7;
    --secondary-200: #ce93d8;
    --secondary-300: #ba68c8;
    --secondary-400: #ab47bc;
    --secondary-500: #9c27b0;
    --secondary-600: #8e24aa;
    --secondary-700: #7b1fa2;
    --secondary-800: #6a1b9a;
    --secondary-900: #4a148c;

    /* Success - Emerald Green (Achievement, Growth) */
    --success-50: #e8f5e8;
    --success-100: #c8e6c9;
    --success-200: #a5d6a7;
    --success-300: #81c784;
    --success-400: #66bb6a;
    --success-500: #4caf50;  /* Perfect green for success */
    --success-600: #43a047;
    --success-700: #388e3c;
    --success-800: #2e7d32;
    --success-900: #1b5e20;

    /* Warning - Amber Gold (Attention, Caution) */
    --warning-50: #fff8e1;
    --warning-100: #ffecb3;
    --warning-200: #ffe082;
    --warning-300: #ffd54f;
    --warning-400: #ffca28;
    --warning-500: #ffc107;  /* Optimal warning color */
    --warning-600: #ffb300;
    --warning-700: #ffa000;
    --warning-800: #ff8f00;
    --warning-900: #ff6f00;

    /* Error - Red Coral (Urgency, Problems) */
    --error-50: #ffebee;
    --error-100: #ffcdd2;
    --error-200: #ef9a9a;
    --error-300: #e57373;
    --error-400: #ef5350;
    --error-500: #f44336;  /* Clear error indication */
    --error-600: #e53935;
    --error-700: #d32f2f;
    --error-800: #c62828;
    --error-900: #b71c1c;

    /* Info - Cyan Blue (Information, Data) */
    --info-50: #e0f2f1;
    --info-100: #b2dfdb;
    --info-200: #80cbc4;
    --info-300: #4db6ac;
    --info-400: #26a69a;
    --info-500: #009688;
    --info-600: #00897b;
    --info-700: #00796b;
    --info-800: #00695c;
    --info-900: #004d40;

    /* Neutral - Sophisticated Grays */
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #eeeeee;
    --neutral-300: #e0e0e0;
    --neutral-400: #bdbdbd;
    --neutral-500: #9e9e9e;
    --neutral-600: #757575;
    --neutral-700: #616161;
    --neutral-800: #424242;
    --neutral-900: #212121;

    /* Glass Effects */
    --glass-light: rgba(255, 255, 255, 0.15);
    --glass-dark: rgba(0, 0, 0, 0.2);
    --glass-border-light: rgba(255, 255, 255, 0.25);
    --glass-border-dark: rgba(255, 255, 255, 0.1);

    /* Shadows */
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    --shadow-neumorphism-light: 20px 20px 60px #d1d5db, -20px -20px 60px #ffffff;
    --shadow-neumorphism-dark: 20px 20px 60px #111827, -20px -20px 60px #374151;
    
    --border-radius-xl: 1.5rem;
    --border-radius-2xl: 2rem;
    --border-radius-3xl: 3rem;
    
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: var(--neutral-800);
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--secondary-600) 50%, var(--primary-700) 100%);
    min-height: 100vh;
    overflow-x: hidden;
}

.dark body {
    color: var(--neutral-100);
    background: linear-gradient(135deg, var(--neutral-900) 0%, var(--primary-900) 50%, var(--secondary-900) 100%);
}

/* ===== MODERN GLASSMORPHISM ===== */
.glass-morphism {
    background: var(--glass-light);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border-light);
    box-shadow: var(--shadow-glass);
    position: relative;
    overflow: hidden;
}

.dark .glass-morphism {
    background: var(--glass-dark);
    border: 1px solid var(--glass-border-dark);
    box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
}

.glass-morphism::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.glass-morphism:hover::before {
    left: 100%;
}

/* ===== ADVANCED NEUMORPHISM ===== */
.neumorphism {
    background: linear-gradient(145deg, #f0f0f0, #cacaca);
    box-shadow: var(--shadow-neumorphism-light);
    border: none;
    transition: var(--transition-smooth);
}

.dark .neumorphism {
    background: linear-gradient(145deg, #2d3748, #1a202c);
    box-shadow: var(--shadow-neumorphism-dark);
}

.neumorphism:hover {
    box-shadow: inset 5px 5px 10px #bebebe, inset -5px -5px 10px #ffffff;
}

.dark .neumorphism:hover {
    box-shadow: inset 5px 5px 10px #1a202c, inset -5px -5px 10px #4a5568;
}

/* ===== MODERN ANIMATIONS ===== */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(-5px) rotate(-1deg); }
}

@keyframes pulse-glow {
    0%, 100% { 
        box-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
        transform: scale(1);
    }
    50% { 
        box-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
        transform: scale(1.05);
    }
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes morph-blob {
    0%, 100% { 
        border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%;
        transform: rotate(0deg) scale(1);
    }
    25% { 
        border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%;
        transform: rotate(90deg) scale(1.1);
    }
    50% { 
        border-radius: 50% 50% 50% 50% / 50% 50% 50% 50%;
        transform: rotate(180deg) scale(0.9);
    }
    75% { 
        border-radius: 70% 30% 40% 60% / 40% 70% 60% 30%;
        transform: rotate(270deg) scale(1.05);
    }
}

@keyframes particle-float {
    0%, 100% { 
        transform: translateY(0px) translateX(0px) rotate(0deg);
        opacity: 0.7;
    }
    25% { 
        transform: translateY(-20px) translateX(10px) rotate(90deg);
        opacity: 1;
    }
    50% { 
        transform: translateY(-10px) translateX(-10px) rotate(180deg);
        opacity: 0.5;
    }
    75% { 
        transform: translateY(-30px) translateX(5px) rotate(270deg);
        opacity: 0.8;
    }
}

@keyframes slide-in-bounce {
    0% {
        transform: translateX(-100%) scale(0.8);
        opacity: 0;
    }
    60% {
        transform: translateX(10%) scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: translateX(0%) scale(1);
        opacity: 1;
    }
}

@keyframes scale-bounce {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes text-glow {
    0%, 100% {
        text-shadow: 0 0 5px rgba(59, 130, 246, 0.5);
    }
    50% {
        text-shadow: 0 0 20px rgba(59, 130, 246, 0.8), 0 0 30px rgba(59, 130, 246, 0.6);
    }
}

/* ===== MODERN UTILITY CLASSES ===== */
.floating-element {
    animation: float 6s ease-in-out infinite;
}

.morphing-blob {
    animation: morph-blob 8s ease-in-out infinite;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
}

.gradient-bg {
    background: linear-gradient(-45deg, var(--primary-500), var(--primary-600), var(--primary-700), var(--primary-800));
    background-size: 400% 400%;
    animation: gradient-shift 15s ease infinite;
}

.text-gradient {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-700) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.dark .text-gradient {
    background: linear-gradient(135deg, var(--primary-400) 0%, var(--primary-300) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hover-lift {
    transition: var(--transition-smooth);
    cursor: pointer;
}

.hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.hover-glow:hover {
    animation: pulse-glow 2s ease-in-out infinite;
}

/* ===== MODERN RIPPLE EFFECT ===== */
.ripple {
    position: relative;
    overflow: hidden;
    transform: translate3d(0, 0, 0);
}

.ripple::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
    pointer-events: none;
}

.ripple:active::before {
    width: 300px;
    height: 300px;
}

/* ===== MODERN CARDS ===== */
.modern-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-2xl);
    padding: 2rem;
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.modern-card::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s;
    pointer-events: none;
}

.modern-card:hover::after {
    opacity: 1;
    animation: particle-float 3s ease-in-out infinite;
}

.modern-card:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: 0 30px 60px -12px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

/* ===== MODERN BUTTONS ===== */
.modern-btn {
    background: linear-gradient(135deg, var(--primary-500) 0%, var(--primary-600) 100%);
    border: none;
    border-radius: var(--border-radius-xl);
    padding: 0.75rem 1.5rem;
    color: white;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.modern-btn-success {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
}

.modern-btn-warning {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
}

.modern-btn-error {
    background: linear-gradient(135deg, var(--error-500) 0%, var(--error-600) 100%);
}

.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.modern-btn:hover::before {
    left: 100%;
}

.modern-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 15px 30px -5px rgba(102, 126, 234, 0.4);
}

.modern-btn:active {
    transform: translateY(0) scale(0.98);
}

/* ===== MODERN PROGRESS INDICATORS ===== */
.modern-progress {
    background: var(--neutral-200);
    border-radius: 9999px;
    overflow: hidden;
    position: relative;
}

.dark .modern-progress {
    background: var(--neutral-700);
}

.modern-progress-fill {
    height: 100%;
    border-radius: 9999px;
    background: linear-gradient(90deg, var(--primary-500), var(--primary-600));
    transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.modern-progress-fill.success {
    background: linear-gradient(90deg, var(--success-500), var(--success-600));
}

.modern-progress-fill.warning {
    background: linear-gradient(90deg, var(--warning-500), var(--warning-600));
}

.modern-progress-fill.error {
    background: linear-gradient(90deg, var(--error-500), var(--error-600));
}

.modern-progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 50px 50px;
    animation: progress-stripes 2s linear infinite;
}

@keyframes progress-stripes {
    0% { background-position: 0 0; }
    100% { background-position: 50px 50px; }
}

/* ===== MODERN TOOLTIPS ===== */
.modern-tooltip {
    position: relative;
    display: inline-block;
}

.modern-tooltip .tooltip-content {
    visibility: hidden;
    width: 200px;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(10px);
    color: #fff;
    text-align: center;
    border-radius: var(--border-radius-xl);
    padding: 12px 16px;
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    margin-left: -100px;
    opacity: 0;
    transition: var(--transition-smooth);
    font-size: 14px;
    line-height: 1.4;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-tooltip .tooltip-content::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
}

.modern-tooltip:hover .tooltip-content {
    visibility: visible;
    opacity: 1;
    transform: translateY(-5px);
}

/* ===== MODERN SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #5a67d8, #6b46c1);
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    .modern-card {
        padding: 1.5rem;
    }
    
    .hover-lift:hover {
        transform: none;
    }
    
    .modern-btn:hover {
        transform: none;
    }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

@media (prefers-contrast: high) {
    .glass-morphism {
        background: rgba(255, 255, 255, 0.9);
        border: 2px solid #000;
    }
    
    .dark .glass-morphism {
        background: rgba(0, 0, 0, 0.9);
        border: 2px solid #fff;
    }
}

/* ===== PRINT STYLES ===== */
@media print {
    body {
        background: white !important;
        color: black !important;
    }
    
    .glass-morphism {
        background: white !important;
        backdrop-filter: none !important;
        border: 1px solid #ccc !important;
        box-shadow: none !important;
    }
    
    .gradient-bg {
        background: #f8fafc !important;
    }
    
    .floating-element,
    .morphing-blob {
        animation: none !important;
    }
}

// 🖼️ IMAGE MANAGEMENT TEST CONFIGURATION
// Following the same pattern as site-editor-config.ts

export const ImageManagementConfig = {
  // Test execution settings
  EXECUTION: {
    TIMEOUT: 60000, // 1 minute per test (reduced from debug timeouts)
    RETRY_ATTEMPTS: 2,
    PARALLEL_EXECUTION: false,
    PERFORMANCE_MODE: true,
  },

  // Timing configurations
  TIMEOUTS: {
    NAVIGATION: 30000,     // Page navigation timeout
    ELEMENT_WAIT: 15000,   // Element visibility timeout
    STEP_WAIT: 2000,       // Wait between steps (reduced from 30s debug)
    UPLOAD_WAIT: 15000,    // File upload completion
    EDITOR_WAIT: 10000,    // Image editor loading
    POPUP_WAIT: 2000,      // Popup detection (reduced from 30s debug)
    DIALOG_WAIT: 5000,     // Dialog appearance
    DELETE_WAIT: 3000,     // Deletion completion

    // 🚀 ENHANCED: WebKit-specific extended timeouts for 100% success
    WEBKIT_ENHANCED: {
      IFRAME_DETECTION: 8000,    // Extended iframe detection for WebKit
      BLOCK_INTERACTION: 5000,   // Extended block interaction timeout
      TEMPLATE_SELECTION: 8000,  // Extended template selection timeout
      IMAGE_INTERACTION: 5000,   // Extended image interaction timeout
      EDITOR_LOADING: 8000,      // Extended editor loading timeout
      FALLBACK_WAIT: 5000        // Extended fallback operation timeout
    }
  },

  // Robust selectors based on working patterns
  SELECTORS: {
    // Image Management Modal
    MODAL: {
      TRIGGER: '画像を管理',
      CONTAINER: [
        '#dataview-1091',
        '.x-panel-body',
        '[class*="image-gallery"]'
      ],
      CLOSE: [
        '.x-tool-close',
        'button:has-text("閉じる")',
        '[data-tooltip="閉じる"]'
      ]
    },

    // Image Upload
    UPLOAD: {
      BUTTON: [
        '[data-tooltip="画像取込み"]',  // Primary working selector
        '#button-1023',               // Fallback ID
        'button:has-text("アップロード")',
        '[class*="upload"]'
      ],
      INPUT: 'input[type="file"]',
      PROGRESS: [
        '.upload-progress',
        '.x-progress-bar',
        '[class*="loading"]'
      ]
    },

    // Image Gallery
    GALLERY: {
      CONTAINER: '#dataview-1091',
      IMAGE_ITEM: '.thumb-wrap',
      IMAGE_SELECTOR: 'img',
      DATA_ATTRIBUTE: 'data-name'
    },

    // Image Actions (based on working selectors)
    ACTIONS: {
      RENAME: [
        '.icon-edit',
        '[class*="rename"]',
        '[data-tooltip*="名前"]',
        '[title*="rename"]'
      ],
      EDIT: [
        '.icon-edit2',
        '[class*="edit"]',
        '[data-tooltip*="編集"]',
        '[title*="edit"]'
      ],
      DELETE: [
        '[data-tooltip="画像削除"]',  // Primary working selector
        '.btn-del-image',            // Secondary working selector
        '.icon-trash',               // Tertiary working selector
        '.cs-footer-button.icon-trash',
        '.x-btn[data-tooltip="画像削除"]'
      ]
    },

    // Dialogs and Popups
    DIALOGS: {
      RENAME: {
        WINDOW: [
          '.x-window.pop-window-folder-input',
          '.x-window:has(text="画像ファイル名編集")',
          '[class*="rename-dialog"]'
        ],
        INPUT: [
          'input.x-form-required-field.x-form-text',
          '.pop-folder-input-name input[type="text"]',
          '.rename-input'
        ],
        CONFIRM: [
          '.x-btn:has-text("OK")',
          'button:has-text("OK")',
          '[data-action="confirm"]'
        ]
      },
      DELETE: {
        CONFIRM: [
          'button:has-text("OK")',
          'button:has-text("はい")',
          '.x-btn:has-text("OK")',
          '[data-action="delete-confirm"]'
        ]
      },
      ERROR: {
        DUPLICATE: [
          'text=既に同じファイル名のファイルがアップロードされています。',
          '.x-form-display-field:has-text("既に同じファイル名")',
          '[class*="error"]:has-text("ファイル名")'
        ],
        CLOSE: [
          'button:has-text("OK")',
          '.x-btn:has-text("OK")',
          '.error-close'
        ]
      }
    },

    // Image Editor (iframe-based)
    EDITOR: {
      IFRAME: 'iframe[name="SignEditorWin"]',
      POPUP_CLOSE: [
        '#id-first-guide-ok',        // Primary working selector
        'button:has-text("閉じる")',
        '.cs-button:has-text("閉じる")'
      ],
      EDIT_PANEL: '.cs-edit-option',
      FILTERS: {
        CONTAINER: '.cs-filter',
        THUMBNAILS: '.cs-filter .cs-thum',
        FIRST_FILTER: '.cs-filter .cs-thum:first-child',
        SECOND_FILTER: '.cs-filter .cs-thum:nth-child(2)'
      },
      EFFECTS: {
        SHADOW: '.cs-edit-option .cs-item[data-key="シャドウ"] .cs-name.cs-checkbox',
        GLOW: '.cs-edit-option .cs-item[data-key="グロー"] .cs-name.cs-checkbox',
        BLUR: '.cs-edit-option .cs-item[data-key="ブラー"] .cs-name.cs-checkbox'
      },
      SAVE_CLOSE: [
        '.x-tool-close',
        'button:has-text("保存")',
        '[data-action="save"]'
      ]
    },

    // Loading and Masks
    LOADING: {
      MASKS: '.x-mask',
      SPINNERS: [
        '.x-mask-loading',
        '.loading-spinner',
        '[class*="loading"]'
      ],
      PROGRESS: [
        '.x-progress-bar',
        '.upload-progress'
      ]
    }
  },

  // Test data paths
  TEST_DATA: {
    IMAGES: {
      SAMPLE: 'test-data/images/Image-Editor-Test-Sample.png',
      SMALL: 'test-data/images/small-test.jpg',
      LARGE: 'test-data/images/large-test.png'
    },
    NAMES: {
      RENAMED: 'test-renamed-image',
      BULK_PREFIX: 'bulk-test-image',
      TEMP_PREFIX: 'temp-test-'
    }
  },

  // Performance settings
  PERFORMANCE: {
    REDUCE_WAITS: true,
    SMART_RETRIES: true,
    MAX_RETRIES: 3,
    PARALLEL_UPLOADS: false,
    BATCH_SIZE: 3,
    CLEANUP_AFTER_EACH: true
  },

  // Error handling
  ERROR_HANDLING: {
    CONTINUE_ON_MINOR_ERRORS: true,
    SCREENSHOT_ON_FAILURE: true,
    DETAILED_LOGGING: true,
    GRACEFUL_DEGRADATION: true
  },

  // Health check settings
  HEALTH_CHECK: {
    ENABLED: true,
    QUICK_CHECK: true,
    FULL_VALIDATION: false,
    CLEANUP_ON_FAILURE: true
  }
};

export type ImageManagementConfigType = typeof ImageManagementConfig;

// Export specific configurations for different test scenarios
export const QuickTestConfig = {
  ...ImageManagementConfig,
  TIMEOUTS: {
    ...ImageManagementConfig.TIMEOUTS,
    STEP_WAIT: 1000,        // Faster for quick tests
    POPUP_WAIT: 1000,       // Faster popup detection
    UPLOAD_WAIT: 10000,     // Shorter upload wait
  },
  PERFORMANCE: {
    ...ImageManagementConfig.PERFORMANCE,
    REDUCE_WAITS: true,
    MAX_RETRIES: 2,
  }
};

export const RobustTestConfig = {
  ...ImageManagementConfig,
  TIMEOUTS: {
    ...ImageManagementConfig.TIMEOUTS,
    STEP_WAIT: 3000,        // Longer for stability
    POPUP_WAIT: 5000,       // Longer popup detection
    UPLOAD_WAIT: 20000,     // Longer upload wait
  },
  EXECUTION: {
    ...ImageManagementConfig.EXECUTION,
    RETRY_ATTEMPTS: 3,
  },
  PERFORMANCE: {
    ...ImageManagementConfig.PERFORMANCE,
    MAX_RETRIES: 5,
  }
};

export const BulkTestConfig = {
  ...ImageManagementConfig,
  PERFORMANCE: {
    ...ImageManagementConfig.PERFORMANCE,
    BATCH_SIZE: 5,
    PARALLEL_UPLOADS: false, // Keep false for stability
    CLEANUP_AFTER_EACH: false, // Cleanup at end for bulk
  },
  TIMEOUTS: {
    ...ImageManagementConfig.TIMEOUTS,
    UPLOAD_WAIT: 25000,     // Longer for bulk operations
  }
};

import { Page, ElementHandle } from 'playwright';

// ==========================================
// Selector Analysis Types
// ==========================================

export interface SelectorAnalysis {
  element: string;
  stability: 'stable' | 'dynamic' | 'unreliable';
  alternatives: string[];
  confidence: number;
  lastSeen: Date;
  frequency: number;
  attributes: ElementAttributes;
  context: string;
}

export interface ElementAttributes {
  id?: string;
  className?: string;
  tagName: string;
  textContent?: string;
  ariaLabel?: string;
  dataAttributes: Record<string, string>;
  position: { x: number; y: number };
  size: { width: number; height: number };
}

export interface SelectorRecommendation {
  selector: string;
  confidence: number;
  reasoning: string;
  fallbacks: string[];
  stability: 'high' | 'medium' | 'low';
}

// ==========================================
// Smart Loading Types
// ==========================================

export interface LoadingStrategy {
  type: 'race' | 'sequential' | 'adaptive' | 'intelligent';
  timeouts: number[];
  fallbacks: string[];
  conditions: LoadingCondition[];
  context: string;
}

export interface LoadingCondition {
  type: 'domcontentloaded' | 'networkidle' | 'element' | 'custom';
  selector?: string;
  timeout: number;
  required: boolean;
}

export interface LoadingMetrics {
  operation: string;
  duration: number;
  success: boolean;
  strategy: string;
  timestamp: Date;
  context: string;
  failureReason?: string;
}

export interface AdaptiveTimingData {
  operation: string;
  averageDuration: number;
  minDuration: number;
  maxDuration: number;
  successRate: number;
  sampleCount: number;
  lastUpdated: Date;
}

// ==========================================
// Debug & Logging Types
// ==========================================

export interface DebugSnapshot {
  timestamp: Date;
  step: string;
  url: string;
  elements: ElementInfo[];
  networkActivity: NetworkEvent[];
  consoleMessages: ConsoleMessage[];
  screenshot?: Buffer;
  domState: string;
  performanceMetrics: PerformanceMetrics;
}

export interface ElementInfo {
  selector: string;
  tagName: string;
  visible: boolean;
  enabled: boolean;
  textContent?: string;
  attributes: Record<string, string>;
  boundingBox?: { x: number; y: number; width: number; height: number };
}

export interface NetworkEvent {
  url: string;
  method: string;
  status: number;
  duration: number;
  timestamp: Date;
  type: 'request' | 'response';
}

export interface ConsoleMessage {
  type: 'log' | 'error' | 'warn' | 'info';
  text: string;
  timestamp: Date;
  location?: string;
}

export interface PerformanceMetrics {
  loadTime: number;
  domContentLoaded: number;
  firstContentfulPaint?: number;
  largestContentfulPaint?: number;
  cumulativeLayoutShift?: number;
  memoryUsage?: number;
}

// ==========================================
// BiNDup-Specific Types
// ==========================================

export interface BiNDupPattern {
  operation: string;
  expectedElements: string[];
  loadingIndicators: string[];
  successCriteria: string[];
  commonFailures: string[];
  averageDuration: number;
  optimizations: string[];
}

export interface BiNDupState {
  phase: 'authentication' | 'launch' | 'navigation' | 'editing' | 'saving';
  loading: boolean;
  popupsPresent: string[];
  errors: string[];
  lastActivity: Date;
}

export interface PopupInfo {
  selector: string;
  type: 'guide' | 'confirmation' | 'error' | 'loading';
  closeSelectors: string[];
  priority: number;
  autoHandle: boolean;
}

// ==========================================
// MCP Tool Input/Output Types
// ==========================================

export interface AnalyzeSelectorInput {
  url?: string;
  targetElement?: string;
  analysisDepth: 'basic' | 'deep' | 'comprehensive';
  context?: string;
}

export interface AnalyzeSelectorOutput {
  recommendations: SelectorRecommendation[];
  analysis: SelectorAnalysis[];
  summary: {
    totalElements: number;
    stableElements: number;
    dynamicElements: number;
    unreliableElements: number;
  };
}

export interface SmartWaitInput {
  operation: string;
  context: string;
  maxWait?: number;
  strategy: 'adaptive' | 'race' | 'sequential' | 'intelligent';
  customConditions?: LoadingCondition[];
}

export interface SmartWaitOutput {
  success: boolean;
  duration: number;
  strategy: string;
  conditions: string[];
  recommendations: string[];
}

export interface DebugFlowInput {
  step: string;
  captureLevel: 'basic' | 'detailed' | 'comprehensive';
  highlightElements?: string[];
  includeScreenshot?: boolean;
  includeNetworkActivity?: boolean;
}

export interface DebugFlowOutput {
  snapshot: DebugSnapshot;
  insights: string[];
  recommendations: string[];
  flowDocumentation: string;
}

export interface BiNDupOptimizeInput {
  operation: 'launch' | 'navigate' | 'edit' | 'save' | 'popup-handle';
  optimizationLevel: 'conservative' | 'balanced' | 'aggressive';
  context?: string;
}

export interface BiNDupOptimizeOutput {
  optimizations: string[];
  estimatedImprovement: number;
  risks: string[];
  recommendations: string[];
}

// ==========================================
// Configuration Types
// ==========================================

export interface MCPServerConfig {
  port: number;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  dataStorage: {
    path: string;
    maxSize: number;
    retention: number; // days
  };
  playwright: {
    headless: boolean;
    timeout: number;
    viewport: { width: number; height: number };
  };
  bindui: {
    baseUrl: string;
    defaultTimeouts: Record<string, number>;
    knownSelectors: Record<string, string[]>;
  };
}

// ==========================================
// Error Types
// ==========================================

export class PlaywrightMCPError extends Error {
  constructor(
    message: string,
    public code: string,
    public context?: any
  ) {
    super(message);
    this.name = 'PlaywrightMCPError';
  }
}

export class SelectorAnalysisError extends PlaywrightMCPError {
  constructor(message: string, context?: any) {
    super(message, 'SELECTOR_ANALYSIS_ERROR', context);
  }
}

export class SmartLoadingError extends PlaywrightMCPError {
  constructor(message: string, context?: any) {
    super(message, 'SMART_LOADING_ERROR', context);
  }
}

export class BiNDupOptimizationError extends PlaywrightMCPError {
  constructor(message: string, context?: any) {
    super(message, 'BINDUI_OPTIMIZATION_ERROR', context);
  }
}

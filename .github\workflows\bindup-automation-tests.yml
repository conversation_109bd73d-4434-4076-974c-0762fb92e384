name: 🚀 BiNDup Automation Framework - Multi-Environment CI/CD

on:
  push:
    branches: [main, develop, feature/*]
  pull_request:
    branches: [main, develop]
  schedule:
    # Run tests daily at 2 AM JST (17:00 UTC)
    - cron: "0 17 * * *"
  workflow_dispatch:
    inputs:
      environment:
        description: "Environment to test"
        required: true
        default: "development"
        type: choice
        options:
          - development
          - staging
          - production
      test_suite:
        description: "Test suite to run"
        required: false
        default: "all"
        type: choice
        options:
          - all
          - site-creation
          - site-editor
          - site-theater
          - image-crud
          - performance
          - security
          - cross-browser
      browsers:
        description: "Browsers to test"
        required: false
        default: "chromium,webkit"
        type: choice
        options:
          - chromium,webkit
          - chromium,webkit,firefox
          - chromium
          - webkit
          - firefox
      parallel_workers:
        description: "Number of parallel workers"
        required: false
        default: "2"
        type: choice
        options:
          - "1"
          - "2"
          - "4"

env:
  NODE_VERSION: "18"
  PLAYWRIGHT_BROWSERS_PATH: ${{ github.workspace }}/ms-playwright

jobs:
  # 🔍 Environment Detection Job
  detect-environment:
    name: 🔍 Detect Environment & Configuration
    runs-on: ubuntu-latest
    outputs:
      environment: ${{ steps.env-detection.outputs.environment }}
      test-suite: ${{ steps.env-detection.outputs.test-suite }}
      browsers: ${{ steps.env-detection.outputs.browsers }}
      workers: ${{ steps.env-detection.outputs.workers }}
      timeout: ${{ steps.env-detection.outputs.timeout }}

    steps:
      - name: 🔍 Detect Environment and Configuration
        id: env-detection
        run: |
          # Determine environment based on trigger
          if [[ "${{ github.event_name }}" == "workflow_dispatch" ]]; then
            ENVIRONMENT="${{ github.event.inputs.environment }}"
            TEST_SUITE="${{ github.event.inputs.test_suite }}"
            BROWSERS="${{ github.event.inputs.browsers }}"
            WORKERS="${{ github.event.inputs.parallel_workers }}"
          elif [[ "${{ github.ref }}" == "refs/heads/main" ]]; then
            ENVIRONMENT="production"
            TEST_SUITE="all"
            BROWSERS="chromium,webkit,firefox"
            WORKERS="2"
          elif [[ "${{ github.ref }}" == "refs/heads/develop" ]]; then
            ENVIRONMENT="staging"
            TEST_SUITE="all"
            BROWSERS="chromium,webkit"
            WORKERS="2"
          elif [[ "${{ github.event_name }}" == "pull_request" ]]; then
            ENVIRONMENT="development"
            TEST_SUITE="site-creation,site-editor"
            BROWSERS="chromium"
            WORKERS="1"
          elif [[ "${{ github.event_name }}" == "schedule" ]]; then
            ENVIRONMENT="production"
            TEST_SUITE="all"
            BROWSERS="chromium,webkit,firefox"
            WORKERS="2"
          else
            ENVIRONMENT="development"
            TEST_SUITE="site-creation"
            BROWSERS="chromium"
            WORKERS="1"
          fi

          # Set timeout based on environment
          case $ENVIRONMENT in
            "production") TIMEOUT="90" ;;
            "staging") TIMEOUT="60" ;;
            *) TIMEOUT="45" ;;
          esac

          echo "environment=$ENVIRONMENT" >> $GITHUB_OUTPUT
          echo "test-suite=$TEST_SUITE" >> $GITHUB_OUTPUT
          echo "browsers=$BROWSERS" >> $GITHUB_OUTPUT
          echo "workers=$WORKERS" >> $GITHUB_OUTPUT
          echo "timeout=$TIMEOUT" >> $GITHUB_OUTPUT

          echo "🔍 Detected Configuration:"
          echo "  Environment: $ENVIRONMENT"
          echo "  Test Suite: $TEST_SUITE"
          echo "  Browsers: $BROWSERS"
          echo "  Workers: $WORKERS"
          echo "  Timeout: ${TIMEOUT}m"
        shell: bash

  # 🧪 Main Test Execution Job
  test-execution:
    name: 🧪 Test Execution
    runs-on: ${{ matrix.os }}
    needs: detect-environment
    timeout-minutes: ${{ fromJson(needs.detect-environment.outputs.timeout) }}

    strategy:
      fail-fast: false
      matrix:
        include:
          # Ubuntu runners - primary testing
          - os: ubuntu-latest
            browser: chromium
          - os: ubuntu-latest
            browser: webkit
          - os: ubuntu-latest
            browser: firefox
          # macOS runner - WebKit native testing
          - os: macos-latest
            browser: webkit
          # Windows runner - Chromium testing
          - os: windows-latest
            browser: chromium

    env:
      ENVIRONMENT: ${{ needs.detect-environment.outputs.environment }}
      TEST_SUITE: ${{ needs.detect-environment.outputs.test-suite }}
      WORKERS: ${{ needs.detect-environment.outputs.workers }}
      BROWSER: ${{ matrix.browser }}
      OS: ${{ matrix.os }}

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: 📦 Install Dependencies
        run: |
          npm ci --prefer-offline --no-audit

      - name: 🎭 Install Playwright Browsers
        run: |
          npx playwright install ${{ matrix.browser }} --with-deps

      - name: 🔧 Setup Test Environment
        run: |
          echo "🔧 Setting up test environment for ${{ env.ENVIRONMENT }}"
          echo "PLAYWRIGHT_HTML_REPORT=playwright-report-${{ matrix.browser }}-${{ matrix.os }}" >> $GITHUB_ENV
          echo "PLAYWRIGHT_JSON_OUTPUT_NAME=results-${{ matrix.browser }}-${{ matrix.os }}.json" >> $GITHUB_ENV

      - name: 📁 Create Test Results Directory
        run: |
          # Create test results directory (cross-platform)
          if [ ! -d "test-results" ]; then
            mkdir test-results
          fi

          # Verify test files exist
          echo "🔍 Verifying test files..."
          if [ -f "tests/e2e/Site-Creation-Test.spec.ts" ]; then
            echo "  ✅ Site-Creation-Test.spec.ts found"
          else
            echo "  ❌ Site-Creation-Test.spec.ts NOT found"
          fi

          if [ -d "tests/e2e" ]; then
            echo "  📁 tests/e2e directory contents:"
            ls -la tests/e2e/ | head -5
          else
            echo "  ❌ tests/e2e directory NOT found"
          fi
        shell: bash

      - name: 🧪 Execute Tests
        run: |
          echo "🧪 Starting test execution..."
          echo "Environment: ${{ env.ENVIRONMENT }}"
          echo "Test Suite: ${{ env.TEST_SUITE }}"
          echo "Browser: ${{ matrix.browser }}"
          echo "OS: ${{ matrix.os }}"
          echo "Workers: ${{ env.WORKERS }}"
          echo "Timestamp: $(date)"

          # Add debugging information
          echo "📊 System Information:"
          echo "  Node version: $(node --version)"
          echo "  NPM version: $(npm --version)"
          echo "  Playwright version: $(npx playwright --version)"
          echo "  Available memory: $(free -h 2>/dev/null || echo 'N/A (macOS/Windows)')"
          echo "  Disk space: $(df -h . | tail -1)"

          # Build test command based on test suite
          TEST_COMMAND="npx playwright test"

          # Add project filter
          TEST_COMMAND="$TEST_COMMAND --project=${{ matrix.browser }}"

          # Add debugging and timeout options
          TEST_COMMAND="$TEST_COMMAND --timeout=60000"
          TEST_COMMAND="$TEST_COMMAND --global-timeout=1800000"

          # Add test suite filter
          if [[ "${{ env.TEST_SUITE }}" != "all" ]]; then
            case "${{ env.TEST_SUITE }}" in
              "site-creation") TEST_COMMAND="$TEST_COMMAND tests/e2e/Site-Creation-Test.spec.ts" ;;
              "site-editor") TEST_COMMAND="$TEST_COMMAND tests/e2e/Site-Editor-Test.spec.ts" ;;
              "site-theater") TEST_COMMAND="$TEST_COMMAND tests/e2e/Site-Theater-Test.spec.ts" ;;
              "image-crud") TEST_COMMAND="$TEST_COMMAND tests/playwright-tests/" ;;
              "performance") TEST_COMMAND="$TEST_COMMAND -g 'SPF-'" ;;
              "security") TEST_COMMAND="$TEST_COMMAND -g 'SSC-'" ;;
              "cross-browser") TEST_COMMAND="$TEST_COMMAND -g 'CBT-'" ;;
            esac
          else
            # For production environment, run minimal test first
            if [[ "${{ env.ENVIRONMENT }}" == "production" ]]; then
              echo "🧪 Production environment detected - running minimal test first"

              # Try to run just one simple test to isolate the issue
              echo "🔍 Testing basic Playwright functionality..."
              MINIMAL_TEST="npx playwright test --project=${{ matrix.browser }} --timeout=30000 --workers=1 --max-failures=1 --reporter=line tests/e2e/Site-Creation-Test.spec.ts -g 'SCT-01'"
              echo "Minimal test command: $MINIMAL_TEST"

              # Run minimal test with detailed error capture
              if eval $MINIMAL_TEST 2>&1; then
                echo "✅ Minimal test passed - proceeding with full suite"
              else
                echo "❌ Minimal test failed - capturing detailed error information"
                echo "🔍 Attempting to run with maximum debugging..."

                # Try with even more basic test
                BASIC_TEST="npx playwright test --list --project=${{ matrix.browser }}"
                echo "Basic test listing: $BASIC_TEST"
                eval $BASIC_TEST 2>&1 || echo "❌ Even basic test listing failed"

                echo "❌ Aborting full test suite due to basic functionality failure"
                exit 1
              fi
            fi
          fi

          # Add execution parameters
          TEST_COMMAND="$TEST_COMMAND --workers=${{ env.WORKERS }}"
          TEST_COMMAND="$TEST_COMMAND --max-failures=3"
          TEST_COMMAND="$TEST_COMMAND --reporter=line,html,json,junit"



          echo "🚀 Executing: $TEST_COMMAND"
          echo "⏰ Start time: $(date)"

          # Execute with timeout monitoring (cross-platform)
          if [[ "${{ matrix.os }}" == "macos-latest" ]]; then
            # macOS - use gtimeout if available, otherwise rely on Playwright timeouts
            if command -v gtimeout &> /dev/null; then
              gtimeout 1500 bash -c "eval $TEST_COMMAND" || {
                echo "❌ Test execution timed out or failed after 25 minutes"
                echo "⏰ End time: $(date)"
                exit 1
              }
            else
              # Fallback: rely on Playwright's built-in timeouts
              eval $TEST_COMMAND || {
                echo "❌ Test execution failed"
                echo "⏰ End time: $(date)"
                exit 1
              }
            fi
          else
            # Linux/Windows - use timeout command
            timeout 25m bash -c "eval $TEST_COMMAND" || {
              echo "❌ Test execution timed out or failed after 25 minutes"
              echo "⏰ End time: $(date)"
              exit 1
            }
          fi

          echo "✅ Test execution completed"
          echo "⏰ End time: $(date)"
        shell: bash
        env:
          CI: true
          PWTEST_HTML_REPORT_OPEN: never

      - name: 📊 Upload Test Results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-${{ matrix.browser }}-${{ matrix.os }}-${{ env.ENVIRONMENT }}
          path: |
            test-results/
            playwright-report*/
          retention-days: 30

      - name: 📈 Upload Performance Data
        uses: actions/upload-artifact@v4
        if: always() && contains(env.TEST_SUITE, 'performance')
        with:
          name: performance-data-${{ matrix.browser }}-${{ matrix.os }}
          path: |
            performance-data.json
            performance-monitor.js
          retention-days: 30

  # 🧹 Cleanup Job
  cleanup:
    name: 🧹 Site Cleanup & Maintenance
    runs-on: ubuntu-latest
    needs: [detect-environment, test-execution]
    if: always() && (needs.detect-environment.outputs.environment == 'staging' || needs.detect-environment.outputs.environment == 'production')
    timeout-minutes: 15

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"

      - name: 📦 Install Dependencies
        run: npm ci --prefer-offline --no-audit

      - name: 🎭 Install Playwright Browsers
        run: npx playwright install chromium --with-deps

      - name: 🧹 Execute Site Cleanup
        run: |
          echo "🧹 Starting automated site cleanup..."
          echo "Environment: ${{ needs.detect-environment.outputs.environment }}"

          # Run the global cleanup utility
          if [ -f "utils/global-cleanup.ts" ]; then
            echo "Running TypeScript cleanup utility..."
            npx ts-node utils/global-cleanup.ts
          elif [ -f "utils/global-cleanup.js" ]; then
            echo "Running JavaScript cleanup utility..."
            node utils/global-cleanup.js
          else
            echo "⚠️ No cleanup utility found, skipping cleanup"
          fi
        env:
          CI: true

      - name: 📊 Upload Cleanup Report
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cleanup-report-${{ needs.detect-environment.outputs.environment }}
          path: |
            cleanup-report.json
            cleanup-log.txt
          retention-days: 7

  # 📊 Reporting & Analytics Job
  generate-report:
    name: 📊 Generate Comprehensive Report
    runs-on: ubuntu-latest
    needs: [detect-environment, test-execution]
    if: always()
    timeout-minutes: 10

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 📥 Download All Test Artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts/
          pattern: test-results-*

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 📊 Generate Comprehensive Report
        run: |
          echo "# 🚀 BiNDup Automation Test Results" > test-summary.md
          echo "" >> test-summary.md
          echo "**Environment:** ${{ needs.detect-environment.outputs.environment }}" >> test-summary.md
          echo "**Test Suite:** ${{ needs.detect-environment.outputs.test-suite }}" >> test-summary.md
          echo "**Browsers:** ${{ needs.detect-environment.outputs.browsers }}" >> test-summary.md
          echo "**Execution Time:** $(date -u)" >> test-summary.md
          echo "**Trigger:** ${{ github.event_name }}" >> test-summary.md
          echo "" >> test-summary.md

          echo "## 📊 Test Execution Summary" >> test-summary.md
          echo "" >> test-summary.md

          # Initialize counters
          TOTAL_BROWSERS=0
          SUCCESSFUL_BROWSERS=0

          # Check results for each browser
          for browser in $(echo "${{ needs.detect-environment.outputs.browsers }}" | tr ',' ' '); do
            TOTAL_BROWSERS=$((TOTAL_BROWSERS + 1))

            if find artifacts/ -name "*$browser*" -type d | grep -q .; then
              echo "✅ **${browser^} Tests**: Executed successfully" >> test-summary.md
              SUCCESSFUL_BROWSERS=$((SUCCESSFUL_BROWSERS + 1))
            else
              echo "❌ **${browser^} Tests**: Failed or not executed" >> test-summary.md
            fi
          done

          echo "" >> test-summary.md
          echo "**Success Rate:** $SUCCESSFUL_BROWSERS/$TOTAL_BROWSERS browsers" >> test-summary.md
          echo "" >> test-summary.md

          # Add performance metrics if available
          if find artifacts/ -name "*performance*" -type d | grep -q .; then
            echo "## ⚡ Performance Metrics" >> test-summary.md
            echo "" >> test-summary.md
            echo "📈 Performance data collected and available in artifacts" >> test-summary.md
            echo "" >> test-summary.md
          fi

          echo "## 🔗 Available Artifacts" >> test-summary.md
          echo "" >> test-summary.md
          echo "- 📊 **Test Reports**: HTML reports for each browser/OS combination" >> test-summary.md
          echo "- 🎥 **Videos**: Recorded for failed tests (debugging)" >> test-summary.md
          echo "- 📸 **Screenshots**: Captured on test failures" >> test-summary.md
          echo "- 🔍 **Traces**: Detailed execution traces for analysis" >> test-summary.md
          echo "- 📋 **JSON Results**: Machine-readable test results" >> test-summary.md

          if find artifacts/ -name "*performance*" -type d | grep -q .; then
            echo "- ⚡ **Performance Data**: Core Web Vitals and load time metrics" >> test-summary.md
          fi

          echo "" >> test-summary.md
          echo "## 🔧 Environment Details" >> test-summary.md
          echo "" >> test-summary.md
          echo "- **Node.js Version:** ${{ env.NODE_VERSION }}" >> test-summary.md
          echo "- **Playwright Version:** $(npm list @playwright/test --depth=0 2>/dev/null | grep @playwright/test || echo 'Not available')" >> test-summary.md
          echo "- **OS Matrix:** Ubuntu (primary), macOS (WebKit), Windows (Edge)" >> test-summary.md
          echo "- **Parallel Workers:** ${{ needs.detect-environment.outputs.workers }}" >> test-summary.md
          echo "- **Timeout:** ${{ needs.detect-environment.outputs.timeout }} minutes" >> test-summary.md

          echo "" >> test-summary.md
          echo "---" >> test-summary.md
          echo "*Generated by BiNDup Automation Framework v2.0*" >> test-summary.md

          cat test-summary.md

      - name: 📊 Upload Comprehensive Report
        uses: actions/upload-artifact@v4
        with:
          name: comprehensive-test-report-${{ needs.detect-environment.outputs.environment }}
          path: test-summary.md
          retention-days: 30

      - name: 💬 Comment on PR (if applicable)
        uses: actions/github-script@v7
        if: github.event_name == 'pull_request'
        with:
          script: |
            const fs = require('fs');
            const summary = fs.readFileSync('test-summary.md', 'utf8');

            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: summary
            });

  # 🔔 Notification Job
  notify:
    name: 🔔 Send Notifications
    runs-on: ubuntu-latest
    needs: [detect-environment, test-execution, generate-report]
    if: always() && (needs.detect-environment.outputs.environment == 'production' || needs.detect-environment.outputs.environment == 'staging')
    timeout-minutes: 5

    steps:
      - name: 📥 Download Test Report
        uses: actions/download-artifact@v4
        with:
          name: comprehensive-test-report-${{ needs.detect-environment.outputs.environment }}
          path: ./

      - name: 🔔 Determine Notification Status
        id: status
        run: |
          # Determine overall status
          if [[ "${{ needs.test-execution.result }}" == "success" ]]; then
            STATUS="✅ SUCCESS"
            COLOR="good"
            EMOJI="🎉"
          elif [[ "${{ needs.test-execution.result }}" == "failure" ]]; then
            STATUS="❌ FAILURE"
            COLOR="danger"
            EMOJI="🚨"
          else
            STATUS="⚠️ PARTIAL"
            COLOR="warning"
            EMOJI="⚠️"
          fi

          echo "status=$STATUS" >> $GITHUB_OUTPUT
          echo "color=$COLOR" >> $GITHUB_OUTPUT
          echo "emoji=$EMOJI" >> $GITHUB_OUTPUT
        shell: bash

      - name: 📧 Send Email Notification (Production Only)
        if: needs.detect-environment.outputs.environment == 'production'
        uses: dawidd6/action-send-mail@v3
        with:
          server_address: ${{ secrets.SMTP_SERVER }}
          server_port: ${{ secrets.SMTP_PORT }}
          username: ${{ secrets.SMTP_USERNAME }}
          password: ${{ secrets.SMTP_PASSWORD }}
          subject: "${{ steps.status.outputs.emoji }} BiNDup Automation Tests - ${{ steps.status.outputs.status }}"
          to: ${{ secrets.NOTIFICATION_EMAIL }}
          from: "BiNDup Automation <${{ secrets.SMTP_USERNAME }}>"
          body: |
            BiNDup Automation Test Results

            Environment: ${{ needs.detect-environment.outputs.environment }}
            Status: ${{ steps.status.outputs.status }}
            Test Suite: ${{ needs.detect-environment.outputs.test-suite }}
            Browsers: ${{ needs.detect-environment.outputs.browsers }}

            Trigger: ${{ github.event_name }}
            Repository: ${{ github.repository }}
            Branch: ${{ github.ref_name }}
            Commit: ${{ github.sha }}

            View detailed results: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}
          attachments: test-summary.md

      - name: 💬 Send Slack Notification
        if: always()
        uses: 8398a7/action-slack@v3
        with:
          status: custom
          custom_payload: |
            {
              "text": "${{ steps.status.outputs.emoji }} BiNDup Automation Tests",
              "attachments": [
                {
                  "color": "${{ steps.status.outputs.color }}",
                  "fields": [
                    {
                      "title": "Status",
                      "value": "${{ steps.status.outputs.status }}",
                      "short": true
                    },
                    {
                      "title": "Environment",
                      "value": "${{ needs.detect-environment.outputs.environment }}",
                      "short": true
                    },
                    {
                      "title": "Test Suite",
                      "value": "${{ needs.detect-environment.outputs.test-suite }}",
                      "short": true
                    },
                    {
                      "title": "Browsers",
                      "value": "${{ needs.detect-environment.outputs.browsers }}",
                      "short": true
                    },
                    {
                      "title": "Repository",
                      "value": "${{ github.repository }}",
                      "short": true
                    },
                    {
                      "title": "Branch",
                      "value": "${{ github.ref_name }}",
                      "short": true
                    }
                  ],
                  "actions": [
                    {
                      "type": "button",
                      "text": "View Results",
                      "url": "${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                    }
                  ]
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # 📈 Performance Monitoring Job
  performance-analysis:
    name: 📈 Performance Analysis
    runs-on: ubuntu-latest
    needs: [detect-environment, test-execution]
    if: always() && contains(needs.detect-environment.outputs.test-suite, 'performance')
    timeout-minutes: 10

    steps:
      - name: 📥 Checkout Repository
        uses: actions/checkout@v4

      - name: 📥 Download Performance Artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts/
          pattern: performance-data-*

      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: 📈 Analyze Performance Data
        run: |
          echo "📈 Analyzing performance data..."

          # Create performance summary
          echo "# 📈 Performance Analysis Report" > performance-summary.md
          echo "" >> performance-summary.md
          echo "**Environment:** ${{ needs.detect-environment.outputs.environment }}" >> performance-summary.md
          echo "**Analysis Date:** $(date -u)" >> performance-summary.md
          echo "" >> performance-summary.md

          # Process performance data files
          if find artifacts/ -name "performance-data.json" | head -1 | xargs test -f; then
            echo "## 🎯 Core Web Vitals" >> performance-summary.md
            echo "" >> performance-summary.md

            # Extract key metrics (this would be enhanced with actual JSON parsing)
            echo "- **First Contentful Paint (FCP)**: Analyzing..." >> performance-summary.md
            echo "- **Largest Contentful Paint (LCP)**: Analyzing..." >> performance-summary.md
            echo "- **Cumulative Layout Shift (CLS)**: Analyzing..." >> performance-summary.md
            echo "- **First Input Delay (FID)**: Analyzing..." >> performance-summary.md
            echo "" >> performance-summary.md

            echo "## 📊 Load Time Analysis" >> performance-summary.md
            echo "" >> performance-summary.md
            echo "- **Average Load Time**: Calculating..." >> performance-summary.md
            echo "- **Performance Score**: Evaluating..." >> performance-summary.md
            echo "" >> performance-summary.md
          else
            echo "⚠️ No performance data found for analysis" >> performance-summary.md
          fi

          echo "## 🔗 Recommendations" >> performance-summary.md
          echo "" >> performance-summary.md
          echo "- Review Core Web Vitals thresholds" >> performance-summary.md
          echo "- Monitor performance trends over time" >> performance-summary.md
          echo "- Consider performance optimizations if metrics exceed thresholds" >> performance-summary.md

          cat performance-summary.md

      - name: 📊 Upload Performance Analysis
        uses: actions/upload-artifact@v4
        with:
          name: performance-analysis-${{ needs.detect-environment.outputs.environment }}
          path: performance-summary.md
          retention-days: 30

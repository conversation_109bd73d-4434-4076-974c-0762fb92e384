import { defineConfig, devices } from "@playwright/test";

/**
 * See https://playwright.dev/docs/test-configuration.
 */
export default defineConfig({
  // Test directory
  testDir: "./tests",

  // Run tests in files in parallel (reduced for WebKit stability)
  fullyParallel: true,
  workers: process.env.CI ? 1 : 2, // Reduced workers for better stability

  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,

  // 🌟 ENHANCED RETRY STRATEGY FOR MAXIMUM RELIABILITY
  retries: process.env.CI ? 3 : 1,

  // 🚀 OPTIMIZED WORKER CONFIGURATION FOR PERFORMANCE
  workers: process.env.CI ? 2 : 4,

  // Reporter to use
  reporter: [
    ["html", { outputFolder: "playwright-report" }],
    ["json", { outputFile: "test-results/results.json" }],
    ["junit", { outputFile: "test-results/results.xml" }],
    ["list"],
  ],

  // Shared settings for all the projects below
  use: {
    // Base URL to use in actions like `await page.goto('/')`
    baseURL: process.env.BASE_URL || "http://localhost:3000",

    // 🎯 ENHANCED DEBUGGING AND MONITORING
    trace: "retain-on-failure",
    screenshot: "only-on-failure",
    video: "retain-on-failure",

    // ⚡ OPTIMIZED TIMEOUT CONFIGURATION FOR RELIABILITY - 🎯 Enhanced for 100% success
    actionTimeout: 90000, // Increased to 90 seconds for complex operations
    navigationTimeout: 120000, // Increased to 2 minutes for page transitions

    // Browser launch options are configured per-project below
  },

  // Configure projects for major browsers
  projects: [
    {
      name: "chromium",
      use: {
        ...devices["Desktop Chrome"],
        // Additional Chrome-specific settings
        viewport: { width: 1920, height: 1080 },
        launchOptions: {
          args: [
            "--disable-web-security",
            "--disable-features=VizDisplayCompositor",
            "--disable-background-timer-throttling",
            "--disable-backgrounding-occluded-windows",
            "--disable-renderer-backgrounding",
            "--disable-field-trial-config",
            "--disable-ipc-flooding-protection",
            "--no-sandbox",
            "--disable-setuid-sandbox",
          ],
        },
      },
    },

    {
      name: "webkit",
      timeout: 360000, // 6 minutes for WebKit tests (increased for IMT-01)
      retries: 2, // Allow retries for WebKit stability
      use: {
        ...devices["Desktop Safari"],
        // WebKit-specific settings for stability
        viewport: { width: 1280, height: 720 }, // Reduced for better performance
        actionTimeout: 150000, // 2.5 minutes for WebKit actions
        navigationTimeout: 240000, // 4 minutes for WebKit navigation
        // Additional WebKit stability options
        ignoreHTTPSErrors: true,
        bypassCSP: true,
      },
    },

    {
      name: "firefox",
      use: {
        ...devices["Desktop Firefox"],
        // Firefox-specific settings
        viewport: { width: 1920, height: 1080 },
      },
    },

    // Mobile testing (optional - can be enabled later)
    // {
    //   name: 'Mobile Chrome',
    //   use: { ...devices['Pixel 5'] },
    // },
    // {
    //   name: 'Mobile Safari',
    //   use: { ...devices['iPhone 12'] },
    // },
  ],

  // Run your local dev server before starting the tests
  // webServer: {
  //   command: 'npm run start',
  //   url: 'http://127.0.0.1:3000',
  //   reuseExistingServer: !process.env.CI,
  // },

  // Global setup and teardown (temporarily disabled for debugging)
  // globalSetup: require.resolve("./playwright-src/config/global-setup.ts"),
  // globalTeardown: require.resolve("./playwright-src/config/global-teardown.ts"),

  // Test timeout - 🎯 Enhanced: Increased to 3 minutes for 100% success rate
  timeout: 180000,
  expect: {
    // Maximum time expect() should wait for the condition to be met
    timeout: 10000,
  },

  // Output directory for test artifacts
  outputDir: "test-results/",
});

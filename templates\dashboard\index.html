<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎭 BiNDup Test Intelligence Dashboard</title>
    
    <!-- Modern CSS Framework -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Chart.js for Beautiful Visualizations -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/chartjs-adapter-date-fns/2.0.0/chartjs-adapter-date-fns.bundle.min.js"></script>
    
    <!-- Custom Styles -->
    <link rel="stylesheet" href="assets/dashboard.css">
    
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-passed { background-color: #10B981; }
        .status-failed { background-color: #EF4444; }
        .status-skipped { background-color: #F59E0B; }
        
        .metric-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .metric-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .progress-ring {
            transform: rotate(-90deg);
        }
        
        .progress-ring-circle {
            transition: stroke-dashoffset 0.35s;
            transform-origin: 50% 50%;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }
        
        .test-suite-card {
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
        }
        
        .test-suite-card:hover {
            border-left-color: #667eea;
            transform: translateX(5px);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Loading Screen -->
    <div id="loading-screen" class="fixed inset-0 bg-gradient-to-br from-blue-600 to-purple-700 flex items-center justify-center z-50">
        <div class="text-center text-white">
            <div class="animate-spin rounded-full h-32 w-32 border-b-2 border-white mx-auto mb-4"></div>
            <h2 class="text-2xl font-bold mb-2">🎭 BiNDup Test Intelligence</h2>
            <p class="text-blue-200">Loading magnificent test results...</p>
        </div>
    </div>

    <!-- Main Dashboard -->
    <div id="dashboard-container" class="hidden min-h-screen">
        <!-- Header -->
        <header class="glass-effect text-white shadow-lg">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-bold">
                            🎭 BiNDup Test Intelligence Dashboard
                        </h1>
                        <span class="ml-4 px-3 py-1 bg-green-500 text-white text-sm rounded-full">
                            <i class="fas fa-check-circle mr-1"></i>
                            Live
                        </span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="text-right">
                            <p class="text-sm text-gray-300">Generated</p>
                            <p class="text-sm font-medium" id="generated-time">Loading...</p>
                        </div>
                        <div class="flex space-x-2">
                            <button id="details-btn" class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-all">
                                <i class="fas fa-list-alt mr-2"></i>
                                Details
                            </button>
                            <button class="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg transition-all">
                                <i class="fas fa-download mr-2"></i>
                                Export Report
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Executive Summary -->
        <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Executive Summary</h2>
                <p class="text-gray-600">Comprehensive overview of BiNDup automation test results</p>
            </div>

            <!-- Key Metrics Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <!-- Overall Pass Rate -->
                <div class="metric-card glass-effect rounded-xl p-6 text-center">
                    <div class="relative w-24 h-24 mx-auto mb-4">
                        <svg class="progress-ring w-24 h-24">
                            <circle class="progress-ring-circle stroke-current text-gray-300" 
                                    stroke-width="4" fill="transparent" r="40" cx="48" cy="48"/>
                            <circle class="progress-ring-circle stroke-current text-green-500" 
                                    stroke-width="4" fill="transparent" r="40" cx="48" cy="48"
                                    id="pass-rate-circle"/>
                        </svg>
                        <div class="absolute inset-0 flex items-center justify-center">
                            <span class="text-2xl font-bold text-gray-900" id="pass-rate-text">0%</span>
                        </div>
                    </div>
                    <h3 class="text-lg font-semibold text-gray-900">Overall Pass Rate</h3>
                    <p class="text-sm text-gray-600">Test Success Percentage</p>
                </div>

                <!-- Total Tests -->
                <div class="metric-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="flex items-center">
                        <div class="p-3 bg-blue-100 rounded-lg">
                            <i class="fas fa-vial text-2xl text-blue-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-2xl font-bold text-gray-900" id="total-tests">0</p>
                            <p class="text-sm text-gray-600">Total Tests</p>
                        </div>
                    </div>
                    <div class="mt-4 flex items-center text-sm">
                        <span class="status-indicator status-passed"></span>
                        <span class="text-green-600 mr-4" id="passed-tests">0 Passed</span>
                        <span class="status-indicator status-failed"></span>
                        <span class="text-red-600" id="failed-tests">0 Failed</span>
                    </div>
                </div>

                <!-- Execution Time -->
                <div class="metric-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="flex items-center">
                        <div class="p-3 bg-purple-100 rounded-lg">
                            <i class="fas fa-clock text-2xl text-purple-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-2xl font-bold text-gray-900" id="execution-time">0m</p>
                            <p class="text-sm text-gray-600">Total Execution</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="flex justify-between text-sm text-gray-600">
                            <span>Avg per test</span>
                            <span id="avg-execution-time">0s</span>
                        </div>
                    </div>
                </div>

                <!-- Quality Score -->
                <div class="metric-card bg-white rounded-xl p-6 shadow-lg">
                    <div class="flex items-center">
                        <div class="p-3 bg-yellow-100 rounded-lg">
                            <i class="fas fa-star text-2xl text-yellow-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-2xl font-bold text-gray-900" id="quality-score">0</p>
                            <p class="text-sm text-gray-600">Quality Score</p>
                        </div>
                    </div>
                    <div class="mt-4">
                        <div class="w-full bg-gray-200 rounded-full h-2">
                            <div class="bg-gradient-to-r from-yellow-400 to-green-500 h-2 rounded-full transition-all duration-500" 
                                 id="quality-progress" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Risk Assessment -->
            <div class="bg-white rounded-xl p-6 shadow-lg mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-bold text-gray-900">
                        <i class="fas fa-shield-alt mr-2 text-blue-600"></i>
                        Risk Assessment
                    </h3>
                    <span class="px-3 py-1 rounded-full text-sm font-medium" id="risk-badge">
                        <i class="fas fa-info-circle mr-1"></i>
                        Calculating...
                    </span>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="text-center p-4 bg-gray-50 rounded-lg">
                        <div class="text-2xl font-bold text-green-600" id="low-risk-count">0</div>
                        <div class="text-sm text-gray-600">Low Risk Tests</div>
                    </div>
                    <div class="text-center p-4 bg-gray-50 rounded-lg">
                        <div class="text-2xl font-bold text-yellow-600" id="medium-risk-count">0</div>
                        <div class="text-sm text-gray-600">Medium Risk Tests</div>
                    </div>
                    <div class="text-center p-4 bg-gray-50 rounded-lg">
                        <div class="text-2xl font-bold text-red-600" id="high-risk-count">0</div>
                        <div class="text-sm text-gray-600">High Risk Tests</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Test Suites Overview -->
        <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="mb-8">
                <h2 class="text-3xl font-bold text-gray-900 mb-2">Test Suites Performance</h2>
                <p class="text-gray-600">Detailed breakdown by test category</p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Test Suites Grid -->
                <div class="space-y-4" id="test-suites-container">
                    <!-- Test suites will be dynamically populated -->
                </div>

                <!-- Test Distribution Chart -->
                <div class="bg-white rounded-xl p-6 shadow-lg">
                    <h3 class="text-xl font-bold text-gray-900 mb-4">
                        <i class="fas fa-chart-pie mr-2 text-blue-600"></i>
                        Test Distribution & Performance
                    </h3>
                    <div class="relative h-64 w-full">
                        <canvas id="performance-chart"></canvas>
                    </div>
                    <div class="mt-4 text-center">
                        <p class="text-sm text-gray-600">Interactive chart showing test distribution and pass rates</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- AI Insights -->
        <section class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="bg-gradient-to-r from-blue-600 to-purple-700 rounded-xl p-8 text-white">
                <div class="flex items-center mb-6">
                    <div class="p-3 bg-white bg-opacity-20 rounded-lg mr-4">
                        <i class="fas fa-brain text-2xl"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold">AI-Powered Insights</h2>
                        <p class="text-blue-200">Intelligent analysis and recommendations</p>
                    </div>
                    <div class="ml-auto">
                        <span class="px-3 py-1 bg-white bg-opacity-20 rounded-full text-sm">
                            <i class="fas fa-check-circle mr-1"></i>
                            <span id="confidence-score">92%</span> Confidence
                        </span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Key Findings -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-lightbulb mr-2"></i>
                            Key Findings
                        </h3>
                        <ul class="space-y-2" id="key-findings">
                            <!-- Dynamically populated -->
                        </ul>
                    </div>

                    <!-- Recommendations -->
                    <div>
                        <h3 class="text-lg font-semibold mb-4">
                            <i class="fas fa-rocket mr-2"></i>
                            Recommendations
                        </h3>
                        <ul class="space-y-2" id="recommendations">
                            <!-- Dynamically populated -->
                        </ul>
                    </div>
                </div>
            </div>
        </section>

        <!-- Detailed Test Steps View -->
        <section id="details-section" class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8 hidden">
            <div class="mb-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h2 class="text-3xl font-bold text-gray-900 mb-2">Detailed Test Execution</h2>
                        <p class="text-gray-600">Gherkin scenarios with step-by-step execution details</p>
                    </div>
                    <button id="close-details-btn" class="bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg transition-all">
                        <i class="fas fa-times mr-2"></i>
                        Close Details
                    </button>
                </div>
            </div>

            <!-- Test Suite Tabs -->
            <div class="mb-6">
                <div class="border-b border-gray-200">
                    <nav class="-mb-px flex space-x-8" id="suite-tabs">
                        <!-- Tabs will be dynamically populated -->
                    </nav>
                </div>
            </div>

            <!-- Test Details Content -->
            <div id="test-details-content" class="space-y-6">
                <!-- Content will be dynamically populated -->
            </div>
        </section>

        <!-- Footer -->
        <footer class="bg-gray-800 text-white py-8">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm">© 2025 WebLife Japan - BiNDup Test Intelligence Dashboard</p>
                        <p class="text-xs text-gray-400 mt-1">Powered by Advanced Analytics & AI</p>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm">Framework Version: v5.1</span>
                        <span class="text-sm">Report ID: <span id="report-id">Loading...</span></span>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- JavaScript -->
    <script src="assets/dashboard-fixed.js"></script>
    <script>
        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof initializeDashboard === 'function') {
                initializeDashboard();
            } else {
                console.error('initializeDashboard function not found');
            }
        });
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en" class="scroll-smooth">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎭 BiNDup Test Intelligence Dashboard - Modern Edition</title>
    
    <!-- Modern CSS Framework -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            darkMode: 'class',
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            900: '#1e3a8a'
                        },
                        glass: {
                            light: 'rgba(255, 255, 255, 0.1)',
                            dark: 'rgba(0, 0, 0, 0.1)'
                        }
                    },
                    animation: {
                        'float': 'float 6s ease-in-out infinite',
                        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
                        'bounce-slow': 'bounce 3s infinite',
                        'spin-slow': 'spin 8s linear infinite',
                        'gradient': 'gradient 15s ease infinite',
                        'slide-up': 'slideUp 0.5s ease-out',
                        'slide-down': 'slideDown 0.5s ease-out',
                        'scale-in': 'scaleIn 0.3s ease-out',
                        'fade-in-up': 'fadeInUp 0.6s ease-out',
                        'morph': 'morph 2s ease-in-out infinite'
                    },
                    backdropBlur: {
                        xs: '2px',
                    }
                }
            }
        }
    </script>
    
    <!-- Modern Icons -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500&display=swap" rel="stylesheet">
    
    <!-- Chart.js with 3D Plugin -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    
    <!-- Modern CSS -->
    <link rel="stylesheet" href="assets/modern-dashboard.css">
    
    <style>
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
        }
        @keyframes gradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        @keyframes slideUp {
            from { transform: translateY(100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        @keyframes slideDown {
            from { transform: translateY(-100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        @keyframes scaleIn {
            from { transform: scale(0.8); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
        @keyframes fadeInUp {
            from { transform: translateY(30px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        @keyframes morph {
            0%, 100% { border-radius: 60% 40% 30% 70% / 60% 30% 70% 40%; }
            50% { border-radius: 30% 60% 70% 40% / 50% 60% 30% 60%; }
        }
        
        .glass-morphism {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        
        .neumorphism {
            background: linear-gradient(145deg, #f0f0f0, #cacaca);
            box-shadow: 20px 20px 60px #bebebe, -20px -20px 60px #ffffff;
        }
        
        .dark .neumorphism {
            background: linear-gradient(145deg, #2d3748, #1a202c);
            box-shadow: 20px 20px 60px #1a202c, -20px -20px 60px #4a5568;
        }
        
        .gradient-bg {
            background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
        }
        
        .floating-element {
            animation: float 6s ease-in-out infinite;
        }
        
        .morphing-blob {
            animation: morph 8s ease-in-out infinite;
            background: linear-gradient(45deg, #667eea, #764ba2);
        }
        
        .text-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hover-lift {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .hover-lift:hover {
            transform: translateY(-8px) scale(1.02);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        }
        
        .ripple {
            position: relative;
            overflow: hidden;
        }
        
        .ripple::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            transform: translate(-50%, -50%);
            transition: width 0.6s, height 0.6s;
        }
        
        .ripple:active::before {
            width: 300px;
            height: 300px;
        }
        
        .particle-bg {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .progress-ring {
            transition: stroke-dashoffset 0.8s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .metric-card {
            position: relative;
            overflow: hidden;
        }
        
        .metric-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.5s;
            opacity: 0;
        }
        
        .metric-card:hover::before {
            animation: shimmer 1.5s ease-in-out;
            opacity: 1;
        }
        
        @keyframes shimmer {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .loading-dots {
            display: inline-block;
        }
        
        .loading-dots::after {
            content: '';
            animation: dots 2s infinite;
        }
        
        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
    </style>
</head>
<body class="font-inter bg-gray-50 dark:bg-gray-900 transition-colors duration-300">
    <!-- Particle Background -->
    <canvas id="particle-canvas" class="particle-bg"></canvas>
    
    <!-- Theme Toggle -->
    <button id="theme-toggle" class="theme-toggle glass-morphism p-3 rounded-full hover-lift ripple">
        <i class="fas fa-moon dark:hidden text-gray-700"></i>
        <i class="fas fa-sun hidden dark:block text-yellow-400"></i>
    </button>
    
    <!-- Loading Screen with Modern Animation -->
    <div id="loading-screen" class="fixed inset-0 gradient-bg flex items-center justify-center z-50">
        <div class="text-center text-white">
            <div class="morphing-blob w-32 h-32 mx-auto mb-8 flex items-center justify-center">
                <i class="fas fa-chart-line text-4xl text-white animate-pulse"></i>
            </div>
            <h2 class="text-3xl font-bold mb-4 animate-fade-in-up">BiNDup Test Intelligence</h2>
            <p class="text-xl mb-6 animate-fade-in-up" style="animation-delay: 0.2s">Loading magnificent insights<span class="loading-dots"></span></p>
            <div class="flex justify-center space-x-2">
                <div class="w-3 h-3 bg-white rounded-full animate-bounce"></div>
                <div class="w-3 h-3 bg-white rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                <div class="w-3 h-3 bg-white rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
            </div>
        </div>
    </div>
    
    <!-- Main Dashboard Container -->
    <div id="dashboard-container" class="hidden min-h-screen">
        <!-- Modern Header with Glassmorphism -->
        <header class="sticky top-0 z-40 glass-morphism border-b border-white/20">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center space-x-4">
                        <div class="floating-element">
                            <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                                <i class="fas fa-chart-line text-white text-lg"></i>
                            </div>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-gradient">BiNDup Intelligence</h1>
                            <p class="text-sm text-gray-600 dark:text-gray-400">Modern Test Analytics</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <!-- Status Indicator -->
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                            <span class="text-sm text-gray-600 dark:text-gray-400">Live</span>
                        </div>
                        
                        <!-- Action Buttons -->
                        <div class="flex space-x-2">
                            <button id="details-btn" class="glass-morphism px-4 py-2 rounded-lg hover-lift ripple transition-all">
                                <i class="fas fa-list-alt mr-2"></i>
                                <span class="hidden sm:inline">Details</span>
                            </button>
                            <button id="export-btn" class="glass-morphism px-4 py-2 rounded-lg hover-lift ripple transition-all">
                                <i class="fas fa-download mr-2"></i>
                                <span class="hidden sm:inline">Export</span>
                            </button>
                            <button id="refresh-btn" class="glass-morphism px-4 py-2 rounded-lg hover-lift ripple transition-all">
                                <i class="fas fa-sync-alt mr-2"></i>
                                <span class="hidden sm:inline">Refresh</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </header>
        
        <!-- Hero Section with Animated Background -->
        <section class="relative py-20 overflow-hidden">
            <div class="absolute inset-0 gradient-bg opacity-90"></div>
            <div class="absolute inset-0">
                <div class="morphing-blob absolute top-10 left-10 w-64 h-64 opacity-20"></div>
                <div class="morphing-blob absolute bottom-10 right-10 w-48 h-48 opacity-20" style="animation-delay: -4s"></div>
            </div>
            
            <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center text-white">
                <h2 class="text-5xl font-bold mb-6 animate-fade-in-up">Test Intelligence Dashboard</h2>
                <p class="text-xl mb-8 animate-fade-in-up" style="animation-delay: 0.2s">Real-time insights powered by AI analytics</p>
                
                <!-- Live Metrics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mt-12">
                    <!-- Total Tests Card -->
                    <div class="glass-morphism rounded-2xl p-6 hover-lift animate-scale-in border border-white/20">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-sky-500/30 rounded-xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-vial text-sky-200 text-xl"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold text-white" id="hero-total-tests">44</div>
                                <div class="text-sm text-sky-100">Total Tests</div>
                            </div>
                        </div>
                        <div class="w-full bg-white/20 rounded-full h-2">
                            <div class="bg-sky-300 h-2 rounded-full transition-all duration-1000 shadow-sm" style="width: 100%"></div>
                        </div>
                    </div>
                    
                    <!-- Pass Rate Card -->
                    <div class="glass-morphism rounded-2xl p-6 hover-lift animate-scale-in border border-white/20" style="animation-delay: 0.1s">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-green-500/30 rounded-xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-check-circle text-green-200 text-xl"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold text-white" id="hero-pass-rate">95%</div>
                                <div class="text-sm text-green-100">Pass Rate</div>
                            </div>
                        </div>
                        <div class="w-full bg-white/20 rounded-full h-2">
                            <div class="bg-green-300 h-2 rounded-full transition-all duration-1000 shadow-sm" style="width: 95%"></div>
                        </div>
                    </div>
                    
                    <!-- Quality Score Card -->
                    <div class="glass-morphism rounded-2xl p-6 hover-lift animate-scale-in border border-white/20" style="animation-delay: 0.2s">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-purple-500/30 rounded-xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-star text-purple-200 text-xl"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold text-white" id="hero-quality-score">92</div>
                                <div class="text-sm text-purple-100">Quality Score</div>
                            </div>
                        </div>
                        <div class="w-full bg-white/20 rounded-full h-2">
                            <div class="bg-purple-300 h-2 rounded-full transition-all duration-1000 shadow-sm" style="width: 92%"></div>
                        </div>
                    </div>
                    
                    <!-- Execution Time Card -->
                    <div class="glass-morphism rounded-2xl p-6 hover-lift animate-scale-in border border-white/20" style="animation-delay: 0.3s">
                        <div class="flex items-center justify-between mb-4">
                            <div class="w-12 h-12 bg-amber-500/30 rounded-xl flex items-center justify-center shadow-lg">
                                <i class="fas fa-clock text-amber-200 text-xl"></i>
                            </div>
                            <div class="text-right">
                                <div class="text-3xl font-bold text-white" id="hero-execution-time">28m</div>
                                <div class="text-sm text-amber-100">Execution Time</div>
                            </div>
                        </div>
                        <div class="w-full bg-white/20 rounded-full h-2">
                            <div class="bg-amber-300 h-2 rounded-full transition-all duration-1000 shadow-sm" style="width: 75%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Content Area -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <!-- Test Suites Grid with Modern Cards -->
            <section class="mb-16">
                <div class="text-center mb-12">
                    <h3 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">Test Suite Performance</h3>
                    <p class="text-xl text-gray-600 dark:text-gray-400">Comprehensive analysis across all test categories</p>
                </div>

                <div id="test-suites-grid" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <!-- Test suite cards will be dynamically generated -->
                </div>
            </section>

            <!-- Interactive Analytics Section -->
            <section class="mb-16">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- 3D Performance Chart -->
                    <div class="glass-morphism rounded-3xl p-8 hover-lift">
                        <div class="flex items-center justify-between mb-6">
                            <h4 class="text-2xl font-bold text-gray-900 dark:text-white">Performance Distribution</h4>
                            <div class="flex space-x-2">
                                <button class="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center hover:bg-blue-500/30 transition-colors">
                                    <i class="fas fa-expand text-blue-500 text-sm"></i>
                                </button>
                                <button class="w-8 h-8 bg-green-500/20 rounded-lg flex items-center justify-center hover:bg-green-500/30 transition-colors">
                                    <i class="fas fa-download text-green-500 text-sm"></i>
                                </button>
                            </div>
                        </div>
                        <div class="relative h-80">
                            <canvas id="modern-performance-chart" class="w-full h-full"></canvas>
                        </div>
                        <div class="mt-4 text-center">
                            <p class="text-sm text-gray-600 dark:text-gray-400">Interactive 3D visualization with real-time updates</p>
                        </div>
                    </div>

                    <!-- Real-time Metrics -->
                    <div class="space-y-6">
                        <!-- Risk Assessment -->
                        <div class="glass-morphism rounded-3xl p-8 hover-lift">
                            <h4 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Risk Assessment</h4>
                            <div class="space-y-4">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 bg-green-400 rounded-full animate-pulse"></div>
                                        <span class="text-gray-700 dark:text-gray-300">Low Risk</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                            <div class="bg-green-400 h-2 rounded-full transition-all duration-1000" style="width: 85%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900 dark:text-white">85%</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 bg-yellow-400 rounded-full animate-pulse"></div>
                                        <span class="text-gray-700 dark:text-gray-300">Medium Risk</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                            <div class="bg-yellow-400 h-2 rounded-full transition-all duration-1000" style="width: 12%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900 dark:text-white">12%</span>
                                    </div>
                                </div>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-3">
                                        <div class="w-4 h-4 bg-red-400 rounded-full animate-pulse"></div>
                                        <span class="text-gray-700 dark:text-gray-300">High Risk</span>
                                    </div>
                                    <div class="flex items-center space-x-2">
                                        <div class="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                            <div class="bg-red-400 h-2 rounded-full transition-all duration-1000" style="width: 3%"></div>
                                        </div>
                                        <span class="text-sm font-medium text-gray-900 dark:text-white">3%</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Live Activity Feed -->
                        <div class="glass-morphism rounded-3xl p-8 hover-lift">
                            <h4 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">Live Activity</h4>
                            <div class="space-y-4">
                                <div class="flex items-start space-x-3 animate-fade-in-up">
                                    <div class="w-2 h-2 bg-green-400 rounded-full mt-2 animate-pulse"></div>
                                    <div>
                                        <p class="text-sm text-gray-700 dark:text-gray-300">Site Creation tests completed successfully</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-500">2 minutes ago</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3 animate-fade-in-up" style="animation-delay: 0.1s">
                                    <div class="w-2 h-2 bg-blue-400 rounded-full mt-2 animate-pulse"></div>
                                    <div>
                                        <p class="text-sm text-gray-700 dark:text-gray-300">Image Management tests running</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-500">5 minutes ago</p>
                                    </div>
                                </div>
                                <div class="flex items-start space-x-3 animate-fade-in-up" style="animation-delay: 0.2s">
                                    <div class="w-2 h-2 bg-yellow-400 rounded-full mt-2 animate-pulse"></div>
                                    <div>
                                        <p class="text-sm text-gray-700 dark:text-gray-300">WebKit compatibility check initiated</p>
                                        <p class="text-xs text-gray-500 dark:text-gray-500">8 minutes ago</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- AI Insights with Modern Design -->
            <section class="mb-16">
                <div class="glass-morphism rounded-3xl p-8 hover-lift">
                    <div class="text-center mb-8">
                        <div class="inline-flex items-center space-x-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white px-4 py-2 rounded-full text-sm font-medium mb-4">
                            <i class="fas fa-brain"></i>
                            <span>AI-Powered Insights</span>
                        </div>
                        <h3 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">Intelligent Analysis</h3>
                        <p class="text-lg text-gray-600 dark:text-gray-400">Machine learning insights and predictive analytics</p>
                    </div>

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Key Findings -->
                        <div class="space-y-6">
                            <h4 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                                Key Findings
                            </h4>
                            <div class="space-y-4" id="modern-key-findings">
                                <!-- Findings will be dynamically populated -->
                            </div>
                        </div>

                        <!-- Smart Recommendations -->
                        <div class="space-y-6">
                            <h4 class="text-xl font-semibold text-gray-900 dark:text-white flex items-center">
                                <i class="fas fa-rocket text-blue-500 mr-2"></i>
                                Smart Recommendations
                            </h4>
                            <div class="space-y-4" id="modern-recommendations">
                                <!-- Recommendations will be dynamically populated -->
                            </div>
                        </div>
                    </div>

                    <!-- Confidence Score -->
                    <div class="mt-8 text-center">
                        <div class="inline-flex items-center space-x-4 bg-gray-100 dark:bg-gray-800 rounded-2xl px-6 py-4">
                            <div class="text-sm text-gray-600 dark:text-gray-400">AI Confidence Score</div>
                            <div class="flex items-center space-x-2">
                                <div class="w-24 bg-gray-300 dark:bg-gray-600 rounded-full h-2">
                                    <div class="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full transition-all duration-1000" style="width: 92%"></div>
                                </div>
                                <span class="text-lg font-bold text-gray-900 dark:text-white">92%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

        <!-- Detailed View Modal -->
        <div id="details-modal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
            <div class="flex items-center justify-center min-h-screen p-4">
                <div class="glass-morphism rounded-3xl w-full max-w-6xl max-h-[90vh] overflow-hidden">
                    <!-- Modal Header -->
                    <div class="flex items-center justify-between p-6 border-b border-white/20">
                        <h3 class="text-2xl font-bold text-gray-900 dark:text-white">Detailed Test Execution</h3>
                        <button id="close-details-modal" class="w-10 h-10 bg-red-500/20 rounded-full flex items-center justify-center hover:bg-red-500/30 transition-colors">
                            <i class="fas fa-times text-red-500"></i>
                        </button>
                    </div>

                    <!-- Modal Content -->
                    <div class="p-6 overflow-y-auto max-h-[70vh]">
                        <!-- Suite Tabs -->
                        <div class="mb-6">
                            <div class="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-2xl p-1" id="modern-suite-tabs">
                                <!-- Tabs will be dynamically populated -->
                            </div>
                        </div>

                        <!-- Test Details Content -->
                        <div id="modern-test-details-content" class="space-y-6">
                            <!-- Content will be dynamically populated -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Modern Footer -->
        <footer class="bg-gray-100 dark:bg-gray-800 py-12">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <div class="flex items-center justify-center space-x-2 mb-4">
                        <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-line text-white"></i>
                        </div>
                        <span class="text-xl font-bold text-gray-900 dark:text-white">BiNDup Intelligence</span>
                    </div>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        Generated at <span id="modern-generated-time">--</span> |
                        Report ID: <span id="modern-report-id" class="font-mono text-sm">--</span>
                    </p>
                    <div class="flex items-center justify-center space-x-6 text-sm text-gray-500 dark:text-gray-500">
                        <span>WebLife Japan</span>
                        <span>•</span>
                        <span>BiNDup Automation Testing</span>
                        <span>•</span>
                        <span>v1.0.0</span>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- Modern JavaScript -->
    <script src="assets/modern-dashboard.js"></script>
    <script>
        // Initialize modern dashboard when page loads
        document.addEventListener('DOMContentLoaded', function() {
            if (typeof initializeModernDashboard === 'function') {
                initializeModernDashboard();
            } else {
                console.error('initializeModernDashboard function not found');
            }
        });
    </script>
</body>
</html>
